# Learning Platform - LearnHub

## Overview

LearnHub is a comprehensive online learning platform that enables users to create personalized learning plans, search and embed YouTube videos, track progress, and achieve educational goals. The application features a modern, responsive design with a focus on user experience and learning analytics.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter for lightweight client-side routing
- **State Management**: TanStack Query (React Query) for server state management
- **UI Framework**: Radix UI primitives with custom Tailwind CSS styling
- **Design System**: shadcn/ui components with a neutral color scheme
- **Build Tool**: Vite for fast development and optimized production builds

### Backend Architecture
- **Runtime**: Node.js with Express.js server
- **Database**: PostgreSQL with Drizzle ORM for type-safe database operations
- **Database Provider**: Neon serverless PostgreSQL
- **Authentication**: Replit Auth with OpenID Connect integration
- **Session Management**: Express sessions with PostgreSQL store
- **API Design**: RESTful APIs with structured error handling

### Key Components

#### Authentication System
- Integrated Replit Auth with OpenID Connect
- Session-based authentication with PostgreSQL session storage
- Automatic token refresh and user profile management
- Protected routes with middleware-based authorization

#### Database Schema
- **Users**: Profile management with firstName, lastName, email, profileImageUrl
- **Learning Plans**: User-created learning paths with title, description, and active status
- **Videos**: YouTube video metadata including youtubeId, title, description, thumbnails
- **Plan Videos**: Many-to-many relationship between plans and videos with ordering
- **Video Progress**: Tracking user progress with currentTime, completion status
- **Achievements**: Gamification system for user engagement
- **Sessions**: Required for Replit Auth session management

#### Video Integration
- YouTube Data API integration for video search and metadata retrieval
- Video embedding with custom player controls
- Progress tracking with time-based completion metrics
- Thumbnail caching and display optimization

#### Progress Tracking
- Real-time progress updates during video playback
- Weekly and daily analytics for learning insights
- Achievement system with various milestone types
- Visual progress indicators and completion tracking

## Data Flow

1. **User Authentication**: Replit Auth → Session Creation → User Profile Retrieval
2. **Learning Plan Creation**: User Input → Validation → Database Storage → UI Update
3. **Video Search**: User Query → YouTube API → Results Display → Video Selection
4. **Video Playback**: Video Selection → YouTube Embed → Progress Tracking → Database Update
5. **Analytics**: Progress Data → Aggregation → Dashboard Display

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: Serverless PostgreSQL client
- **drizzle-orm**: Type-safe ORM with PostgreSQL dialect
- **@tanstack/react-query**: Server state management and caching
- **express**: Web application framework
- **passport**: Authentication middleware

### UI Dependencies
- **@radix-ui/react-***: Accessible UI primitives
- **tailwindcss**: Utility-first CSS framework
- **class-variance-authority**: Component variant utilities
- **lucide-react**: Icon library

### Development Dependencies
- **typescript**: Type safety across the application
- **vite**: Fast build tool and development server
- **tsx**: TypeScript execution for server development

## Deployment Strategy

### Development Environment
- Replit-optimized development setup with hot module replacement
- Vite development server with proxy configuration
- Environment-specific configuration management
- Database migrations with Drizzle Kit

### Production Build
- Vite production build with code splitting and optimization
- ESBuild for server-side bundle generation
- Static asset optimization and caching
- Environment variable management for sensitive data

### Database Management
- Drizzle migrations for schema updates
- Connection pooling for optimal performance
- Environment-based database URL configuration
- Session cleanup and maintenance procedures

## Changelog
- July 03, 2025. Initial setup
- July 05, 2025. Created three distinct page views:
  - My Plans: Dedicated page for managing learning plans with add/remove course functionality
  - Explore: Video browsing and search with YouTube integration and content recommendations  
  - Progress: Analytics dashboard with weekly/monthly progress tracking and achievements
- Added plan sharing functionality with unique shareable links
- Enhanced navigation with proper routing to separate page components
- July 07, 2025. Implemented AI-powered content recommendation mood board:
  - Created AI Mood Board component with 6 mood categories (focused, creative, motivational, relaxing, trending, discovery)
  - Added intelligent scoring system based on keywords, user preferences, and time context
  - Implemented mood-based recommendation API endpoints with personalized scoring
  - Added user mood profile tracking with completion rates and learning patterns
  - Integrated AI insights providing personalized learning recommendations
  - Enhanced theme system with 8 modern theme options (midnight, ocean, sunset, forest, lavender, rose, etc.)
- July 07, 2025. Implemented collaborative learning network and learning path visualizer:
  - Extended database schema with 7 new tables: friendships, friend_invites, learning_paths, path_nodes, study_groups, study_group_members, group_activities
  - Created comprehensive Friends Network component with friend requests, email invites, and social connections
  - Built Learning Path Visualizer with animated SVG-based path visualization, milestone tracking, and progress animation
  - Added extensive API endpoints for friend management, learning path operations, and study group functionality
  - Transformed Library page into Learning Hub with three tabs: Video Library, Friends Network, and Learning Paths
  - Integrated real-time progress tracking with visual feedback and completion animations
- July 07, 2025. Implemented AI-powered content difficulty optimizer:
  - Extended database schema with 3 new tables: userSkillProfiles, videoDifficultyAnalysis, learningAdaptations
  - Created intelligent video difficulty analysis based on title, description, and content keywords
  - Built comprehensive user skill profiling system tracking skill level, confidence, and completion rates
  - Implemented adaptive learning recommendations that optimize video ordering based on user skill progression
  - Added learning adaptation tracking with user feedback integration for continuous improvement
  - Created DifficultyOptimizer component with 4 tabs: Overview, Skills, Insights, and Progression
  - Added AI-powered struggle pattern analysis and personalized learning insights
  - Integrated difficulty progression paths with optimal learning sequences
- July 07, 2025. Fixed learning path visualizer errors and added notes functionality:
  - Resolved "failed to add learning path" error with improved error handling and validation
  - Added notes field to learningPaths database schema for user annotations
  - Enhanced learning path creation with better error logging and data validation  
  - Implemented SendGrid email service integration for friend invitation system
  - Added Notes UI to learning path visualizer with save functionality
  - Created comprehensive email templates for friend invitations with proper styling

## User Preferences

Preferred communication style: Simple, everyday language.