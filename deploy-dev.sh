#!/bin/bash

# DEVELOPMENT DEPLOYMENT SCRIPT
# This script deploys the application to development EC2 server on port 3000
# Usage: ./deploy-dev.sh
# Run as root user or with sudo

set -e  # Exit on any error

echo "🚀 DEVELOPMENT DEPLOYMENT SCRIPT"
echo "================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Auto-detect application directory
APP_DIR=""
if [ -d "/root/learniify" ]; then
    APP_DIR="/root/learniify"
elif [ -d "/home/<USER>/learniify" ]; then
    APP_DIR="/home/<USER>/learniify"
elif [ -d "/home/<USER>/learniify" ]; then
    APP_DIR="/home/<USER>/learniify"
elif [ -f "package.json" ]; then
    APP_DIR="$(pwd)"
else
    print_error "Cannot find learniify directory!"
    exit 1
fi

cd "$APP_DIR"
print_status "Deploying from: $(pwd)"

# Backup current deployment
if [ -d "dist" ]; then
    print_status "Creating backup..."
    cp -r dist dist.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true
fi

# Stop ALL PM2 processes (clean slate)
print_status "Stopping all processes..."
pm2 stop all 2>/dev/null || true
pm2 delete all 2>/dev/null || true
pkill -f "node.*dist/index.js" 2>/dev/null || true
pkill -f "tsx.*server/index.ts" 2>/dev/null || true
lsof -ti:3000 | xargs kill -9 2>/dev/null || true

# Git operations
print_status "Updating code from Git..."
if [ -d ".git" ]; then
    git stash push -m "Auto-stash $(date)" 2>/dev/null || true
    git fetch origin
    git reset --hard origin/main
    print_success "Code updated from Git"
else
    print_warning "Not a Git repository"
fi

# Install dependencies
print_status "Installing dependencies..."
npm install --production=false
print_success "Dependencies installed"

# Clean ALL build artifacts
print_status "Cleaning build artifacts..."
rm -rf dist/ client/dist/ node_modules/.cache/ build-output/ 2>/dev/null || true

# Build application
print_status "Building application..."
npm run build

# Verify server build
if [ ! -f "dist/index.js" ]; then
    print_error "Server build failed!"
    exit 1
fi

# DEVELOPMENT BUILD STRUCTURE FIX
print_status "Auto-fixing build structure for development..."

# Function to find and copy index.html
fix_build_structure() {
    local found=false
    
    # Check all possible locations
    if [ -f "dist/index.html" ]; then
        print_success "✅ dist/index.html already exists"
        found=true
    elif [ -f "dist/public/index.html" ]; then
        print_status "📁 Found in dist/public/ - copying..."
        cp dist/public/index.html dist/index.html
        cp -r dist/public/assets dist/ 2>/dev/null || true
        found=true
        print_success "✅ Copied from dist/public/"
    elif [ -f "client/dist/index.html" ]; then
        print_status "📁 Found in client/dist/ - copying..."
        cp client/dist/index.html dist/index.html
        cp -r client/dist/assets dist/ 2>/dev/null || true
        found=true
        print_success "✅ Copied from client/dist/"
    elif [ -f "client/dist/public/index.html" ]; then
        print_status "📁 Found in client/dist/public/ - copying..."
        cp client/dist/public/index.html dist/index.html
        cp -r client/dist/public/assets dist/ 2>/dev/null || true
        found=true
        print_success "✅ Copied from client/dist/public/"
    fi
    
    return $($found && echo 0 || echo 1)
}

# Try to fix build structure
if ! fix_build_structure; then
    print_warning "index.html not found, trying manual vite build..."
    
    # Try different vite build commands
    npx vite build --outDir=dist/public 2>/dev/null || \
    npx vite build --outDir=client/dist 2>/dev/null || \
    npx vite build --outDir=build-output 2>/dev/null || true
    
    # Try again to find files
    if ! fix_build_structure; then
        # Last resort - find any index.html and copy it
        INDEX_FILE=$(find . -name "index.html" -type f | grep -v node_modules | head -1)
        if [ -n "$INDEX_FILE" ]; then
            print_status "📁 Found index.html at: $INDEX_FILE"
            cp "$INDEX_FILE" dist/index.html
            
            # Copy assets if they exist
            ASSETS_DIR=$(dirname "$INDEX_FILE")/assets
            if [ -d "$ASSETS_DIR" ]; then
                cp -r "$ASSETS_DIR" dist/ 2>/dev/null || true
            fi
            print_success "✅ Emergency copy successful"
        else
            print_error "❌ No index.html found anywhere!"
            exit 1
        fi
    fi
fi

# Final verification
if [ ! -f "dist/index.html" ]; then
    print_error "❌ Build structure fix failed!"
    exit 1
fi

print_success "✅ Build structure verified: dist/index.html exists"

# Setup development environment file
print_status "Setting up development environment..."
if [ -f ".env.dev" ]; then
    cp .env.dev .env
    print_success "✅ Copied .env.dev to .env"
else
    print_warning "⚠️  .env.dev not found, using existing .env"
fi

# Ensure port 3000 in environment
if [ -f ".env" ]; then
    sed -i 's/PORT=8080/PORT=3000/g' .env
    sed -i 's/PORT=80/PORT=3000/g' .env
    # Add PORT if not exists
    if ! grep -q "PORT=" .env; then
        echo "PORT=3000" >> .env
    fi
else
    echo "PORT=3000" >> .env
    echo "NODE_ENV=development" >> .env
fi
print_success "✅ Port configured to 3000 for development"

# Start application in development mode
print_status "Starting development application..."
PORT=3000 NODE_ENV=development pm2 start npm --name "learniify-dev" -- start

# Wait for startup
sleep 5

# Verify startup
if ! pm2 list | grep -q "online"; then
    print_error "❌ PM2 startup failed!"
    pm2 logs --lines 10
    exit 1
fi

# Save PM2 config
pm2 save
pm2 startup 2>/dev/null || true

# Test the deployment
print_status "Testing development deployment..."
sleep 2

if curl -f -s -I http://localhost:3000 > /dev/null; then
    print_success "✅ Development server responding on port 3000!"
else
    print_warning "⚠️  HTTP test failed, checking logs..."
    pm2 logs --lines 5
fi

# Get public IP
PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "YOUR-EC2-IP")

# Final status
echo ""
echo "🎉 DEVELOPMENT DEPLOYMENT COMPLETED!"
echo "===================================="
echo "✅ Application Directory: $APP_DIR"
echo "✅ Environment: Development"
echo "✅ Git: Updated"
echo "✅ Dependencies: Installed"
echo "✅ Build: Successful"
echo "✅ Structure: Fixed"
echo "✅ PM2: Running"
echo "✅ Development Server: Port 3000"
echo ""
echo "🌐 ACCESS YOUR DEVELOPMENT APPLICATION:"
echo "   Local:  http://localhost:3000"
echo "   Public: http://$PUBLIC_IP:3000"
echo ""
echo "📊 Current Status:"
pm2 status
echo ""
echo "📋 Useful Commands:"
echo "   pm2 status           - Check status"
echo "   pm2 logs             - View logs"
echo "   pm2 restart all      - Restart"
echo "   ./deploy-dev.sh      - Deploy again"
echo "   pm2 logs learniify-dev --follow  - Follow dev logs"
echo ""
print_success "🚀 DEVELOPMENT DEPLOYMENT COMPLETED SUCCESSFULLY!"