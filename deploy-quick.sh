#!/bin/bash

# Quick Deploy Script for Minor Changes
# Usage: ./deploy-quick.sh
# For small UI changes, component updates, etc.

set -e

echo "⚡ Quick Deploy - Minor Changes"
echo "=============================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

# Auto-detect app directory
APP_DIR=""
if [ -d "/root/learniify" ]; then
    APP_DIR="/root/learniify"
elif [ -d "/home/<USER>/learniify" ]; then
    APP_DIR="/home/<USER>/learniify"
elif [ -f "package.json" ]; then
    APP_DIR="$(pwd)"
else
    echo "❌ Cannot find app directory!"
    exit 1
fi

cd "$APP_DIR"
print_status "Working in: $(pwd)"

# Quick git pull
print_status "Pulling latest changes..."
git stash 2>/dev/null || true
git pull origin main

# Quick build
print_status "Building..."
npm run build

# Fix build structure (quick check)
if [ -f "client/dist/public/index.html" ] && [ ! -f "dist/index.html" ]; then
    print_status "Fixing build structure..."
    cp client/dist/public/index.html dist/index.html
    cp -r client/dist/public/assets dist/ 2>/dev/null || true
fi

# Quick restart
print_status "Restarting services..."
pm2 restart all

# Quick test
sleep 2
if curl -f -s -I http://localhost:3000 > /dev/null; then
    print_success "✅ Deployment successful!"
    echo "🌐 App is live at http://localhost:3000"
else
    echo "⚠️  Check logs: pm2 logs"
fi

pm2 status
print_success "⚡ Quick deploy completed!"