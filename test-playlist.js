#!/usr/bin/env node

// Simple test script to verify playlist functionality
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testPlaylistEndpoints() {
  console.log('🧪 Testing Playlist Endpoints...\n');

  try {
    // Test 1: Get common playlist
    console.log('1. Testing GET /api/common-playlist');
    const playlistResponse = await fetch(`${BASE_URL}/api/common-playlist`, {
      credentials: 'include'
    });
    
    if (playlistResponse.ok) {
      const playlist = await playlistResponse.json();
      console.log(`✅ Found ${playlist.length} playlist items`);
      
      if (playlist.length > 0) {
        console.log(`   First item: "${playlist[0].title}" (${playlist[0].category})`);
      }
    } else {
      console.log(`❌ Failed: ${playlistResponse.status} ${playlistResponse.statusText}`);
    }

    // Test 2: Get learning plans (requires authentication)
    console.log('\n2. Testing GET /api/learning-plans');
    const plansResponse = await fetch(`${BASE_URL}/api/learning-plans`, {
      credentials: 'include'
    });
    
    if (plansResponse.status === 401) {
      console.log('⚠️  Authentication required - this is expected for unauthenticated requests');
    } else if (plansResponse.ok) {
      const plans = await plansResponse.json();
      console.log(`✅ Found ${plans.length} learning plans`);
    } else {
      console.log(`❌ Failed: ${plansResponse.status} ${plansResponse.statusText}`);
    }

    console.log('\n🎉 Test completed!');
    console.log('\nTo test the full functionality:');
    console.log('1. Start the server: npm run dev');
    console.log('2. Open browser and login');
    console.log('3. Navigate to the common playlist page');
    console.log('4. Try adding a playlist item to a plan');
    console.log('5. Check browser network tab for any errors');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\nMake sure the server is running on port 3000');
  }
}

// Run the test
testPlaylistEndpoints();