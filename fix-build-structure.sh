#!/bin/bash

# Permanent Build Structure Fix Script
# This script automatically detects and fixes the index.html location issue

set -e

echo "🔧 Fixing build structure permanently..."

APP_DIR="/home/<USER>/learniify"
cd "$APP_DIR"

# Function to fix build structure
fix_build_structure() {
    echo "📁 Analyzing build structure..."
    
    # Check where index.html actually exists
    if [ -f "dist/index.html" ]; then
        echo "✅ index.html found in dist/ - structure is correct"
        return 0
    elif [ -f "dist/public/index.html" ]; then
        echo "📋 index.html found in dist/public/ - copying to dist/"
        cp dist/public/index.html dist/index.html
        cp -r dist/public/assets dist/ 2>/dev/null || true
        echo "✅ Fixed: Copied index.html to dist/"
        return 0
    elif [ -f "client/dist/index.html" ]; then
        echo "📋 index.html found in client/dist/ - copying to dist/"
        mkdir -p dist
        cp client/dist/index.html dist/index.html
        cp -r client/dist/assets dist/ 2>/dev/null || true
        echo "✅ Fixed: Copied from client/dist/ to dist/"
        return 0
    else
        echo "❌ index.html not found anywhere - need to rebuild"
        return 1
    fi
}

# Main execution
echo "🚀 Starting permanent build fix..."

# Stop PM2 processes
echo "⏹️  Stopping PM2 processes..."
pm2 stop all 2>/dev/null || true

# Try to fix existing build first
if fix_build_structure; then
    echo "✅ Build structure fixed!"
else
    echo "🔨 Rebuilding application..."
    
    # Clean and rebuild
    rm -rf dist/ client/dist/ node_modules/.cache/ 2>/dev/null || true
    
    # Install and build
    npm install
    npm run build
    
    # Fix structure after build
    if ! fix_build_structure; then
        echo "❌ Build failed - index.html still not found"
        exit 1
    fi
fi

# Verify the fix
if [ ! -f "dist/index.html" ]; then
    echo "❌ Fix failed - dist/index.html still missing"
    exit 1
fi

echo "✅ Build structure verified - dist/index.html exists"

# Fix permissions
echo "🔐 Fixing permissions..."
sudo chown -R ec2-user:ec2-user "$APP_DIR" 2>/dev/null || chown -R ec2-user:ec2-user "$APP_DIR" 2>/dev/null || true

# Start PM2 processes
echo "🚀 Starting PM2 processes..."
pm2 start npm --name "learniify-http" -- start 2>/dev/null || pm2 restart learniify-http

# Wait for startup
sleep 3

# Test the fix
echo "🧪 Testing the fix..."
if curl -f -s -I http://localhost:3000 > /dev/null; then
    echo "✅ SUCCESS: Server is responding correctly!"
    echo "🌐 Your app is now working at http://localhost:3000"
else
    echo "⚠️  Server started but may have issues. Check logs:"
    pm2 logs --lines 5
fi

# Show final status
echo ""
echo "📊 Final Status:"
pm2 status

echo ""
echo "🎉 Build structure fix completed!"
echo "📝 This script can be run anytime you face this issue."
echo ""
echo "💡 To prevent future issues, always run this script after deployment:"
echo "   ./fix-build-structure.sh"