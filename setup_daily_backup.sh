#!/bin/bash

# Setup Daily Backup Cron Job for Learniify
# This script configures automatic daily backups at 2 AM

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_SCRIPT="$SCRIPT_DIR/backup-supabase.sh"

echo "🕐 Setting up daily backup cron job..."
echo "📁 Script location: $BACKUP_SCRIPT"

# Check if backup script exists
if [ ! -f "$BACKUP_SCRIPT" ]; then
    echo "❌ Backup script not found: $BACKUP_SCRIPT"
    exit 1
fi

# Make backup script executable
chmod +x "$BACKUP_SCRIPT"

# Create cron job entry
CRON_JOB="0 2 * * * cd $SCRIPT_DIR && ./backup-supabase.sh >> /tmp/learniify_backup.log 2>&1"

# Check if cron job already exists
if crontab -l 2>/dev/null | grep -q "backup-supabase.sh"; then
    echo "⚠️  Cron job already exists. Updating..."
    # Remove existing job and add new one
    (crontab -l 2>/dev/null | grep -v "backup-supabase.sh"; echo "$CRON_JOB") | crontab -
else
    echo "➕ Adding new cron job..."
    # Add new job to existing crontab
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
fi

echo ""
echo "✅ Daily backup configured successfully!"
echo "⏰ Schedule: Every day at 2:00 AM"
echo "📝 Logs: /tmp/learniify_backup.log"
echo ""
echo "📋 Current cron jobs:"
crontab -l | grep -E "(backup-supabase|learniify)" || echo "   No backup jobs found"
echo ""
echo "🔧 To modify or remove:"
echo "   View: crontab -l"
echo "   Edit: crontab -e"
echo "   Remove: crontab -l | grep -v backup-supabase.sh | crontab -"