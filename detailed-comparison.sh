#!/bin/bash

# Detailed database comparison
set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PROD_DB_URL="postgresql://postgres.pwaqpoqjmxxlcrxtbjkw:<EMAIL>:6543/postgres"
SECONDARY_DB_URL="postgresql://postgres.nxdobqmglaywashgaack:<EMAIL>:5432/postgres"

echo -e "${BLUE}📋 Detailed Database Comparison${NC}"
echo "=============================================="

# Check recent data to see if backup captured latest changes
echo -e "${YELLOW}🕒 Checking recent data...${NC}"

echo "Production - Latest 3 videos:"
psql "$PROD_DB_URL" -c "SELECT id, title, created_at FROM public.videos ORDER BY created_at DESC LIMIT 3;"

echo -e "\nSecondary - Latest 3 videos:"
psql "$SECONDARY_DB_URL" -c "SELECT id, title, created_at FROM public.videos ORDER BY created_at DESC LIMIT 3;"

echo -e "\n${YELLOW}📊 Table-by-table comparison:${NC}"

tables=("users" "videos" "learning_plans" "plan_videos" "video_progress" "achievements" "favorite_videos")

for table in "${tables[@]}"; do
    prod_count=$(psql "$PROD_DB_URL" -t -c "SELECT COUNT(*) FROM public.$table;" 2>/dev/null | xargs)
    sec_count=$(psql "$SECONDARY_DB_URL" -t -c "SELECT COUNT(*) FROM public.$table;" 2>/dev/null | xargs)
    
    if [ "$prod_count" = "$sec_count" ]; then
        echo -e "${GREEN}✅ $table: $prod_count = $sec_count${NC}"
    else
        echo -e "${RED}❌ $table: $prod_count ≠ $sec_count (diff: $((prod_count - sec_count)))${NC}"
    fi
done

echo -e "\n${BLUE}🎯 Summary:${NC}"
echo "The backup transferred most data successfully but some recent entries may be missing."
echo "This could be due to ongoing activity during the backup process."