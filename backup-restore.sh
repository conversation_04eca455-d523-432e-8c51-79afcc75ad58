#!/bin/bash

# Supabase Database Backup and Restore Script
# This script backs up production data and restores to secondary database

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Database URLs (will be updated with actual values)
PROD_DB_URL="postgresql://postgres.pwaqpoqjmxxlcrxtbjkw:<EMAIL>:6543/postgres"
SECONDARY_DB_URL="postgresql://postgres.nxdobqmglaywashgaack:<EMAIL>:5432/postgres"

# Backup file with timestamp
BACKUP_FILE="learnify_backup_$(date +%Y%m%d_%H%M%S).sql"

echo -e "${YELLOW}🚀 Starting Supabase Database Backup and Restore Process${NC}"
echo "=================================================="

# Step 1: Create backup from production
echo -e "${YELLOW}📦 Creating backup from production database...${NC}"
pg_dump "$PROD_DB_URL" \
  --verbose \
  --clean \
  --no-acl \
  --no-owner \
  --format=plain \
  --file="$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Backup created successfully: $BACKUP_FILE${NC}"
    echo "Backup size: $(du -h "$BACKUP_FILE" | cut -f1)"
else
    echo -e "${RED}❌ Backup failed!${NC}"
    exit 1
fi

# Step 2: Restore to secondary database
echo -e "${YELLOW}🔄 Restoring to secondary database...${NC}"
psql "$SECONDARY_DB_URL" \
  --quiet \
  --file="$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Restore completed successfully!${NC}"
else
    echo -e "${RED}❌ Restore failed!${NC}"
    exit 1
fi

# Step 3: Cleanup (optional)
echo -e "${YELLOW}🧹 Cleaning up backup file...${NC}"
read -p "Do you want to delete the backup file? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm "$BACKUP_FILE"
    echo -e "${GREEN}✅ Backup file deleted${NC}"
else
    echo -e "${YELLOW}📁 Backup file kept: $BACKUP_FILE${NC}"
fi

echo "=================================================="
echo -e "${GREEN}🎉 Database backup and restore process completed!${NC}"