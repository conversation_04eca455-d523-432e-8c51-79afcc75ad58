#!/bin/bash

# Working deployment - only manages existing learniify-http and https processes
echo "🚀 Starting deployment..."

# Check current PM2 processes
echo "📋 Current PM2 processes:"
pm2 list

# Pull latest changes
echo "📥 Pulling latest changes..."
git pull origin main

# Install any new dependencies
echo "📦 Installing dependencies..."
npm install

# Build client
echo "🏗️ Building client..."
# Clear all caches first
rm -rf dist/
rm -rf client/dist/
rm -rf node_modules/.cache/
rm -rf client/node_modules/.cache/ 2>/dev/null || true

# Build with verbose output
npm run build:client

# Verify the build contains our API fix
echo "🔍 Verifying build contains mood API fix..."
if grep -r "recommendations/mood.*selectedMood.*currentTimeContext" dist/ >/dev/null 2>&1; then
    echo "✅ Mood API fix found in build"
else
    echo "❌ Mood API fix NOT found in build - this is a problem!"
    echo "📁 Checking what's in the build:"
    find dist/ -name "*.js" -exec grep -l "recommendations/mood" {} \; | head -3
fi

# Check if build was successful - check multiple possible locations
if [ -f "dist/index.html" ]; then
    echo "✅ Build successful - index.html found in dist/"
    echo "📁 Build output:"
    ls -la dist/ | head -5
elif [ -f "dist/public/index.html" ]; then
    echo "✅ Build successful - index.html found in dist/public/"
    echo "📁 Build output:"
    ls -la dist/public/ | head -5
elif [ -f "client/dist/index.html" ]; then
    echo "✅ Build successful - index.html found in client/dist/"
    echo "📁 Moving build files to correct location..."
    rm -rf dist/
    mv client/dist dist/
    echo "📁 Build output:"
    ls -la dist/ | head -5
else
    echo "❌ Build failed - index.html not found in expected locations"
    echo "📁 Checking all possible locations:"
    echo "dist/:"
    ls -la dist/ 2>/dev/null || echo "dist/ directory doesn't exist"
    echo "dist/public/:"
    ls -la dist/public/ 2>/dev/null || echo "dist/public/ directory doesn't exist"
    echo "client/dist/:"
    ls -la client/dist/ 2>/dev/null || echo "client/dist/ directory doesn't exist"
    exit 1
fi

echo "✅ Build successful"

# Only restart existing processes - never create new ones
echo "🔄 Restarting existing PM2 processes..."

# Restart learniify-http if it exists
if pm2 list | grep -q "learniify-http"; then
    echo "🔄 Restarting learniify-http..."
    pm2 restart learniify-http
else
    echo "❌ ERROR: learniify-http process not found!"
    echo "ℹ️ Please make sure learniify-http process is running before deployment."
    echo "ℹ️ You can start it manually with:"
    echo "   NODE_ENV=production pm2 start 'node --import tsx/esm server/index.ts' --name 'learniify-http'"
    exit 1
fi

# Restart https if it exists (check for both possible names)
if pm2 list | grep -q "learniify-https"; then
    echo "🔄 Restarting learniify-https process..."
    pm2 restart learniify-https
elif pm2 list | grep -q "https"; then
    echo "🔄 Restarting https process..."
    pm2 restart https
else
    echo "ℹ️ No https process found - skipping https restart"
fi

# Clean up any unwanted learniify process (without the -http suffix)
if pm2 list | grep -q "^.*learniify[^-]"; then
    echo "🧹 Cleaning up duplicate 'learniify' process..."
    pm2 delete learniify 2>/dev/null || echo "No learniify process to clean up"
fi

# Save PM2 configuration
pm2 save

echo "✅ Deployment completed!"
echo "📊 PM2 Status:"
pm2 status

# Show logs for active processes
echo "📝 Recent logs for learniify-http:"
pm2 logs learniify-http --lines 10

if pm2 list | grep -q "learniify-https"; then
    echo "📝 Recent logs for learniify-https:"
    pm2 logs learniify-https --lines 5
elif pm2 list | grep -q "https"; then
    echo "📝 Recent logs for https:"
    pm2 logs https --lines 5
fi

echo ""
echo "🎉 Deployment successful! Your application has been updated."
echo "🌐 HTTP: Available on port 3000"
if pm2 list | grep -q "learniify-https\|https"; then
    echo "🔒 HTTPS: Available on port 443"
fi