// Add this to your auth service file
const AUTH_BASE_URL = import.meta.env.VITE_AUTH_API_URL || 'http://localhost:3000';
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

// Update all auth API calls to use AUTH_BASE_URL
const authEndpoints = {
  login: `${AUTH_BASE_URL}/api/auth/login`,
  signup: `${AUTH_BASE_URL}/api/auth/signup`, 
  user: `${AUTH_BASE_URL}/api/auth/user`,
  logout: `${AUTH_BASE_URL}/api/auth/logout`,
  google: `${AUTH_BASE_URL}/api/auth/google`,
};

// Regular API calls use API_BASE_URL (CloudFront)
const apiEndpoints = {
  learningPlans: `${API_BASE_URL}/api/learning-plans`,
  // ... other endpoints
};
