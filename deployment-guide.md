# AWS Amplify Deployment Guide with Aurora Serverless

## Prerequisites
- GitHub account
- AWS account with billing enabled
- Node.js application (LearnHub)
- PostgreSQL database (currently using Neon)

## Part 1: Database Migration to Aurora Serverless

### Step 1: Create Aurora Serverless v2 Cluster

1. **Login to AWS Console**
   - Navigate to RDS service
   - Select "Create database"

2. **Database Configuration**
   - Engine type: PostgreSQL
   - Edition: Aurora (Serverless)
   - Version: PostgreSQL 15.4 (or latest compatible)
   - Templates: Production or Dev/Test

3. **Aurora Serverless v2 Settings**
   - DB cluster identifier: `learnhub-aurora-cluster`
   - Master username: `learnhub_admin`
   - Master password: Generate secure password (save it!)
   - Capacity settings:
     - Minimum: 0.5 ACU
     - Maximum: 2-4 ACU (adjust based on needs)

4. **Network & Security**
   - VPC: Default VPC (or create new)
   - Subnet group: Default
   - Public access: Yes (for migration)
   - VPC security group: Create new
     - Name: `learnhub-aurora-sg`
     - Allow inbound PostgreSQL (5432) from your IP and 0.0.0.0/0 (temporarily)

5. **Additional Configuration**
   - Initial database name: `learnhub`
   - Backup retention: 7 days
   - Monitoring: Enable Performance Insights

### Step 2: Export Current Database

1. **Get Current Database Dump**
   ```bash
   # From your Replit environment, export current database
   pg_dump $DATABASE_URL > learnhub_backup.sql
   ```

2. **Download the backup file** to your local machine

### Step 3: Import to Aurora

1. **Connect to Aurora cluster**
   ```bash
   psql -h your-aurora-writer-endpoint.region.rds.amazonaws.com -U learnhub_admin -d learnhub
   ```

2. **Import the data**
   ```bash
   psql -h your-aurora-writer-endpoint.region.rds.amazonaws.com -U learnhub_admin -d learnhub < learnhub_backup.sql
   ```

3. **Verify the import**
   ```sql
   \dt  -- List tables
   SELECT COUNT(*) FROM users;
   SELECT COUNT(*) FROM learning_plans;
   ```

## Part 2: GitHub Repository Setup

### Step 1: Create GitHub Repository

1. **Create new repository** on GitHub
   - Name: `learnhub-app`
   - Visibility: Private (recommended)
   - Initialize with README: No

### Step 2: Push Code to GitHub

1. **Initialize git in your project** (if not already done)
   ```bash
   git init
   git add .
   git commit -m "Initial commit: LearnHub learning platform"
   ```

2. **Add GitHub remote and push**
   ```bash
   git remote add origin https://github.com/yourusername/learnhub-app.git
   git branch -M main
   git push -u origin main
   ```

### Step 3: Create Amplify Configuration

Create `amplify.yml` in your project root:

```yaml
version: 1
applications:
  - frontend:
      phases:
        preBuild:
          commands:
            - npm ci
        build:
          commands:
            - npm run build
      artifacts:
        baseDirectory: client/dist
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
    backend:
      phases:
        preBuild:
          commands:
            - npm ci
        build:
          commands:
            - npm run build:server
      artifacts:
        baseDirectory: ./
        files:
          - server/**/*
          - package.json
          - package-lock.json
```

## Part 3: AWS Amplify Setup

### Step 1: Create Amplify App

1. **Navigate to AWS Amplify Console**
   - Select "Create new app"
   - Choose "Host web app"

2. **Connect Repository**
   - Source: GitHub
   - Authorize AWS Amplify to access your GitHub
   - Select repository: `learnhub-app`
   - Branch: `main`

### Step 2: Configure Build Settings

1. **App name**: `LearnHub`

2. **Environment variables** (Add these in Amplify Console):
   ```
   NODE_ENV=production
   DATABASE_URL=postgresql://learnhub_admin:<EMAIL>:5432/learnhub
   SESSION_SECRET=your-generated-session-secret
   YOUTUBE_API_KEY=your-youtube-api-key
   SENDGRID_API_KEY=your-sendgrid-api-key (if using email)
   OPENAI_API_KEY=your-openai-api-key (if using AI features)
   REPLIT_DOMAINS=your-amplify-domain.com
   ISSUER_URL=https://replit.com/oidc
   REPL_ID=your-app-id
   ```

3. **Custom Build Settings** (if needed):
   ```yaml
   version: 1
   applications:
     - frontend:
         phases:
           preBuild:
             commands:
               - cd client && npm ci
           build:
             commands:
               - cd client && npm run build
         artifacts:
           baseDirectory: client/dist
           files:
             - '**/*'
         cache:
           paths:
             - client/node_modules/**/*
   ```

### Step 3: Advanced Settings

1. **Custom Domain** (Optional)
   - Add your custom domain
   - Configure DNS settings

2. **Environment Management**
   - Create staging environment from develop branch
   - Configure different environment variables for staging

## Part 4: Authentication Configuration

### Step 1: Update Replit Auth Settings

Since you're moving from Replit, you'll need to:

1. **Option A: Keep Replit Auth**
   - Update `REPLIT_DOMAINS` to include your Amplify domain
   - Configure CORS settings

2. **Option B: Migrate to AWS Cognito** (Recommended for production)
   - Create Cognito User Pool
   - Update authentication middleware
   - Migrate user data

### Step 2: Security Group Updates

Update Aurora security group to allow access only from Amplify:
- Remove 0.0.0.0/0 access
- Add Amplify's IP ranges (or use VPC)

## Part 5: Deployment & Testing

### Step 1: Deploy

1. **Trigger deployment**
   - Push changes to main branch
   - Monitor build in Amplify Console

2. **Build logs monitoring**
   - Check for any build errors
   - Verify environment variables are set

### Step 2: Post-Deployment Verification

1. **Database connectivity test**
   ```bash
   # Test database connection from deployed app
   curl https://your-app.amplifyapp.com/api/health
   ```

2. **Functionality testing**
   - User authentication
   - Learning plan creation
   - Video search and playback
   - Progress tracking

## Part 6: Production Optimizations

### Step 1: Database Security

1. **Remove public access** from Aurora
2. **Set up VPC endpoints** for secure communication
3. **Enable encryption** at rest and in transit

### Step 2: Performance Optimization

1. **CloudFront CDN** (included with Amplify)
2. **Database connection pooling**
3. **Caching strategies**

### Step 3: Monitoring & Logging

1. **CloudWatch logs** for application monitoring
2. **Aurora Performance Insights**
3. **Amplify monitoring** for build and hosting metrics

## Part 7: Cost Optimization

### Aurora Serverless v2 Cost Management
- Set appropriate min/max ACU limits
- Monitor usage patterns
- Consider pausing/scaling during low usage

### Amplify Cost Management
- Monitor build minutes
- Optimize build processes
- Use appropriate instance types

## Troubleshooting Common Issues

### Database Connection Issues
- Check security group settings
- Verify DATABASE_URL format
- Test connection from Amplify environment

### Build Failures
- Review build logs in Amplify Console
- Check Node.js version compatibility
- Verify all dependencies are in package.json

### Authentication Issues
- Update REPLIT_DOMAINS environment variable
- Check callback URLs configuration
- Verify session secret configuration

## Security Checklist

- [ ] Database credentials secured in environment variables
- [ ] Aurora cluster not publicly accessible
- [ ] Security groups properly configured
- [ ] HTTPS enforced
- [ ] Environment variables encrypted
- [ ] Regular security updates scheduled

This guide should help you successfully deploy your LearnHub application to AWS Amplify with Aurora Serverless backend!