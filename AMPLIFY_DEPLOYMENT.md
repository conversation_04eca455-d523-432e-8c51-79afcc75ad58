# AWS Amplify Deployment Checklist

## Prerequisites Setup

### 1. Database Setup (Choose One)
**Option A: Amazon RDS PostgreSQL**
- Create RDS PostgreSQL instance
- Configure security groups for Amplify access
- Update DATABASE_URL in environment variables

**Option B: Amazon Aurora Serverless v2**
- Create Aurora Serverless cluster
- Configure VPC and security groups
- More cost-effective for variable workloads

### 2. Required AWS Services
- [ ] AWS Amplify (for hosting)
- [ ] Amazon RDS or Aurora (database)
- [ ] AWS Systems Manager Parameter Store (for secrets)

## Environment Variables Required

Set these in Amplify Console > App Settings > Environment Variables:

```
NODE_ENV=production
DATABASE_URL=********************************/dbname
SESSION_SECRET=your-32-char-secret
YOUTUBE_API_KEY=your-youtube-key
SENDGRID_API_KEY=your-sendgrid-key (optional)
OPENAI_API_KEY=your-openai-key (optional)
REPLIT_DOMAINS=your-app.amplifyapp.com
ISSUER_URL=https://replit.com/oidc
REPL_ID=your-app-id
```

## Deployment Steps

### 1. Push to GitHub
```bash
git add .
git commit -m "Prepare for Amplify deployment"
git push origin main
```

### 2. Create Amplify App
1. Go to AWS Amplify Console
2. Choose "Host web app"
3. Connect your GitHub repository
4. Select the main branch

### 3. Configure Build Settings
- Build command: `npm run build`
- Build output directory: `dist/public`
- Node.js version: 18.x or 20.x

### 4. Set Environment Variables
Add all required environment variables in Amplify Console

### 5. Deploy and Test
- Monitor build logs
- Test all functionality
- Check database connectivity

## Post-Deployment Tasks

### 1. Database Migration
```bash
# Run database migrations
npm run db:push
```

### 2. Security Configuration
- [ ] Remove public database access
- [ ] Configure proper security groups
- [ ] Enable HTTPS redirect
- [ ] Set up custom domain (optional)

### 3. Monitoring Setup
- [ ] Enable CloudWatch logs
- [ ] Set up error alerts
- [ ] Monitor database performance

## Troubleshooting

### Common Issues
1. **Build Failures**: Check Node.js version compatibility
2. **Database Connection**: Verify security group settings
3. **Environment Variables**: Ensure all required vars are set
4. **Authentication**: Update REPLIT_DOMAINS with Amplify URL

### Build Logs Location
- Amplify Console > App > Build History > View Logs

## Cost Optimization
- Use Aurora Serverless v2 for variable workloads
- Monitor Amplify build minutes
- Set up CloudWatch billing alerts