#!/bin/bash

# EC2 Backup Setup Script
# Run this on your EC2 instance to set up automatic backups

echo "🚀 Setting up Learniify backup on EC2..."

# Install required packages
echo "📦 Installing required packages..."
sudo yum update -y
sudo yum install -y postgresql15 cronie

# Start and enable cron service
echo "⏰ Starting cron service..."
sudo systemctl start crond
sudo systemctl enable crond

# Create backup directory
mkdir -p /home/<USER>/learniify-backup
cd /home/<USER>/learniify-backup

# Create the backup script
cat > backup_learniify.sh << 'EOF'
#!/bin/bash

# Learniify Database Backup Script for EC2
set -e

# Configuration
DB_URL="postgresql://postgres.pwaqpoqjmxxlcrxtbjkw:<EMAIL>:6543/postgres"
BACKUP_DIR="/home/<USER>/learniify-backup/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
DATE_FOLDER=$(date +%Y/%m)
BACKUP_NAME="learniify_backup_${TIMESTAMP}"

# S3 Configuration
S3_BUCKET="db-backup-learniify"
S3_REGION="ap-south-1"
S3_PATH="s3://${S3_BUCKET}/database-backups/${DATE_FOLDER}/"

# Create backup directory
mkdir -p "$BACKUP_DIR"

echo "🚀 Starting Learniify database backup..."
echo "📅 Timestamp: $TIMESTAMP"

# Create custom format backup
echo "📦 Creating custom format backup..."
pg_dump "$DB_URL" \
  --verbose \
  --clean \
  --no-acl \
  --no-owner \
  --format=custom \
  --file="$BACKUP_DIR/${BACKUP_NAME}.dump"

# Create SQL text backup
echo "📝 Creating SQL text backup..."
pg_dump "$DB_URL" \
  --verbose \
  --clean \
  --no-acl \
  --no-owner \
  --file="$BACKUP_DIR/${BACKUP_NAME}.sql"

# Compress SQL backup
echo "🗜️  Compressing SQL backup..."
gzip "$BACKUP_DIR/${BACKUP_NAME}.sql"

# Upload to S3
echo "☁️  Uploading backups to S3..."
aws s3 cp "$BACKUP_DIR/${BACKUP_NAME}.dump" "${S3_PATH}${BACKUP_NAME}.dump" \
  --region "$S3_REGION" \
  --storage-class STANDARD_IA

aws s3 cp "$BACKUP_DIR/${BACKUP_NAME}.sql.gz" "${S3_PATH}${BACKUP_NAME}.sql.gz" \
  --region "$S3_REGION" \
  --storage-class STANDARD_IA

# Verify uploads
if aws s3 ls "${S3_PATH}${BACKUP_NAME}.dump" --region "$S3_REGION" > /dev/null 2>&1; then
  echo "✅ Custom backup verified in S3"
else
  echo "❌ Custom backup upload failed!"
  exit 1
fi

if aws s3 ls "${S3_PATH}${BACKUP_NAME}.sql.gz" --region "$S3_REGION" > /dev/null 2>&1; then
  echo "✅ SQL backup verified in S3"
else
  echo "❌ SQL backup upload failed!"
  exit 1
fi

# Clean up local files
echo "🧹 Cleaning up local backup files..."
rm -f "$BACKUP_DIR/${BACKUP_NAME}.dump"
rm -f "$BACKUP_DIR/${BACKUP_NAME}.sql.gz"

echo "🎉 Backup completed successfully!"
echo "📊 S3 Location: s3://$S3_BUCKET/database-backups/$DATE_FOLDER/"
EOF

# Make script executable
chmod +x backup_learniify.sh

# Test the backup script
echo "🧪 Testing backup script..."
./backup_learniify.sh

# Set up cron job
echo "⏰ Setting up daily cron job..."
CRON_JOB="0 2 * * * /home/<USER>/learniify-backup/backup_learniify.sh >> /home/<USER>/learniify-backup/backup.log 2>&1"

# Check if crontab is available
if command -v crontab >/dev/null 2>&1; then
    # Add to crontab
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    echo "✅ Cron job added successfully"
else
    echo "⚠️  Crontab not available, setting up systemd timer instead..."
    
    # Create systemd service
    sudo tee /etc/systemd/system/learniify-backup.service > /dev/null << 'SYSTEMD_SERVICE'
[Unit]
Description=Learniify Database Backup
Wants=learniify-backup.timer

[Service]
Type=oneshot
User=ec2-user
ExecStart=/home/<USER>/learniify-backup/backup_learniify.sh
StandardOutput=append:/home/<USER>/learniify-backup/backup.log
StandardError=append:/home/<USER>/learniify-backup/backup.log
SYSTEMD_SERVICE

    # Create systemd timer
    sudo tee /etc/systemd/system/learniify-backup.timer > /dev/null << 'SYSTEMD_TIMER'
[Unit]
Description=Run Learniify backup daily at 2 AM
Requires=learniify-backup.service

[Timer]
OnCalendar=*-*-* 02:00:00
Persistent=true

[Install]
WantedBy=timers.target
SYSTEMD_TIMER

    # Enable and start the timer
    sudo systemctl daemon-reload
    sudo systemctl enable learniify-backup.timer
    sudo systemctl start learniify-backup.timer
    
    echo "✅ Systemd timer configured successfully"
fi

echo ""
echo "✅ EC2 backup setup completed!"
echo "⏰ Schedule: Every day at 2:00 AM UTC"
echo "📝 Logs: /home/<USER>/learniify-backup/backup.log"
echo "📁 S3 Bucket: s3://db-backup-learniify"
echo ""
echo "🔧 Useful commands:"
if command -v crontab >/dev/null 2>&1; then
    echo "   Check cron: crontab -l"
else
    echo "   Check timer: sudo systemctl status learniify-backup.timer"
    echo "   View timer logs: sudo journalctl -u learniify-backup.service"
fi
echo "   View logs: tail -f /home/<USER>/learniify-backup/backup.log"
echo "   Test backup: /home/<USER>/learniify-backup/backup_learniify.sh"