// Utility functions for handling video durations

export function parseYouTubeDuration(duration: string): number {
  if (!duration) return 0;
  
  // Parse YouTube duration format (PT4M13S) to seconds
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!match) return 0;
  
  const hours = parseInt(match[1]) || 0;
  const minutes = parseInt(match[2]) || 0;
  const seconds = parseInt(match[3]) || 0;
  
  return hours * 3600 + minutes * 60 + seconds;
}

export function formatDuration(totalSeconds: number): string {
  if (totalSeconds === 0) return "0 min";
  
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes} min`;
}

export function calculateTotalDuration(videos: Array<{ duration?: string }>): string {
  const totalSeconds = videos.reduce((total, video) => {
    return total + parseYouTubeDuration(video.duration || "");
  }, 0);
  
  return formatDuration(totalSeconds);
}