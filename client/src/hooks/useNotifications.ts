import { useQuery } from "@tanstack/react-query";
import { useAuth } from "./useAuth";

export interface Notification {
  id: number;
  type: 'friend_request' | 'friend_accepted' | 'achievement' | 'plan_shared' | 'video_completed' | 'milestone';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  data?: any;
}

export function useNotifications() {
  const { isAuthenticated } = useAuth();

  const { data: notifications = [], isLoading, refetch } = useQuery({
    queryKey: ['/api/notifications'],
    enabled: isAuthenticated,
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const unreadCount = (notifications || []).filter((n: Notification) => !n.isRead).length;

  return {
    notifications,
    unreadCount,
    isLoading,
    refetch,
  };
}