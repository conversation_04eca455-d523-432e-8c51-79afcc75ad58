import { useQuery } from "@tanstack/react-query";
import type { User } from "@shared/schema";

export function useAuth() {
  const { data: user, isLoading, error } = useQuery<User>({
    queryKey: ["/api/auth/user"],
    retry: false,
    staleTime: 5000,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  // If there's an error, user is not authenticated
  const isAuthenticated = !!user && !error;
  
  return {
    user: isAuthenticated ? user : null,
    isLoading,
    isAuthenticated,
  };
}
