import React, { createContext, useContext, useEffect, useState } from 'react';

type BaseTheme = 'light' | 'modern-blue' | 'modern-purple' | 'modern-green' | 'modern-orange';
type Theme = BaseTheme | 'dark';

interface ThemeContextType {
  theme: Theme;
  isDark: boolean;
  setTheme: (theme: Theme) => void;
  toggleDark: () => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setThemeState] = useState<Theme>(() => {
    const saved = localStorage.getItem('theme');
    return (saved as Theme) || 'light';
  });
  
  const [isDark, setIsDark] = useState(() => {
    const saved = localStorage.getItem('isDark');
    return saved === 'true';
  });

  useEffect(() => {
    localStorage.setItem('theme', theme);
    localStorage.setItem('isDark', isDark.toString());
    
    // Remove all theme classes
    document.documentElement.classList.remove('dark', 'modern-blue', 'modern-purple', 'modern-green', 'modern-orange');
    
    // Add current theme class
    if (theme !== 'light') {
      document.documentElement.classList.add(theme);
    }
    
    // Add dark class if dark mode is enabled
    if (isDark) {
      document.documentElement.classList.add('dark');
    }
  }, [theme, isDark]);

  const setTheme = (newTheme: Theme) => {
    if (newTheme === 'dark') {
      setIsDark(true);
      setThemeState('light');
    } else {
      setThemeState(newTheme);
      if (newTheme === 'light') {
        setIsDark(false);
      }
    }
  };

  const toggleDark = () => {
    setIsDark(prev => !prev);
  };

  const toggleTheme = () => {
    if (theme === 'light' && !isDark) {
      setIsDark(true);
    } else {
      setTheme('light');
      setIsDark(false);
    }
  };

  const currentTheme = isDark && theme === 'light' ? 'dark' : theme;

  return (
    <ThemeContext.Provider value={{ theme: currentTheme, isDark, setTheme, toggleDark, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}