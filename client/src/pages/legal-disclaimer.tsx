import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle, ExternalLink, Shield, Youtube } from "lucide-react";
import { useLocation } from "wouter";

export default function LegalDisclaimer() {
  const [, setLocation] = useLocation();
  
  return (
    <div className="min-h-screen bg-slate-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <Shield className="w-12 h-12 text-blue-600 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900">Legal Disclaimer</h1>
          <p className="text-gray-600 mt-2">Important legal information about Learniify</p>
        </div>

        <div className="space-y-6">
          {/* YouTube API Compliance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Youtube className="w-5 h-5 text-red-600" />
                YouTube API Services Compliance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-red-800">Important Notice</h4>
                    <p className="text-red-700 text-sm mt-1">
                      This application uses YouTube API Services. By using Learniify, you agree to be bound by the YouTube Terms of Service.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-3">
                <p><strong>YouTube Terms of Service:</strong></p>
                <a 
                  href="https://www.youtube.com/t/terms" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline flex items-center gap-1"
                >
                  YouTube Terms of Service <ExternalLink className="w-4 h-4" />
                </a>
                
                <p><strong>Google Privacy Policy:</strong></p>
                <a 
                  href="https://policies.google.com/privacy" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline flex items-center gap-1"
                >
                  Google Privacy Policy <ExternalLink className="w-4 h-4" />
                </a>
              </div>
            </CardContent>
          </Card>

          {/* Content Ownership */}
          <Card>
            <CardHeader>
              <CardTitle>Content Ownership & Copyright</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3 text-sm">
                <p><strong>Video Content:</strong> All video content displayed on Learniify is owned by the respective creators and is subject to YouTube's copyright policies. Learniify does not claim ownership of any video content.</p>
                
                <p><strong>Educational Use:</strong> Learniify is an educational platform that helps users organize and track their learning progress using publicly available YouTube videos.</p>
                
                <p><strong>No Content Hosting:</strong> Learniify does not host, store, or redistribute any video content. All videos are streamed directly from YouTube's servers.</p>
                
                <p><strong>DMCA Compliance:</strong> If you believe your copyrighted work has been used inappropriately, please contact us <NAME_EMAIL></p>
              </div>
            </CardContent>
          </Card>

          {/* Monetization Disclaimer */}
          <Card>
            <CardHeader>
              <CardTitle>Monetization & Revenue</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3 text-sm">
                <p><strong>Platform Revenue:</strong> Learniify generates revenue through:</p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>Voluntary donations from users</li>
                  <li>Google AdSense advertisements (for non-supporters)</li>
                  <li>Premium features and services</li>
                </ul>
                
                <p><strong>No Revenue from Video Content:</strong> Learniify does not monetize or profit directly from the video content created by others. All video monetization remains with the original creators through YouTube's platform.</p>
                
                <p><strong>Educational Purpose:</strong> Revenue is used solely to maintain and improve the educational platform, including server costs, development, and user support.</p>
              </div>
            </CardContent>
          </Card>

          {/* User Responsibilities */}
          <Card>
            <CardHeader>
              <CardTitle>User Responsibilities</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3 text-sm">
                <p><strong>Compliance:</strong> Users must comply with YouTube's Terms of Service and Community Guidelines when using embedded videos.</p>
                
                <p><strong>Respect Copyright:</strong> Users should respect the intellectual property rights of content creators.</p>
                
                <p><strong>Educational Use:</strong> The platform is intended for educational and personal learning purposes only.</p>
                
                <p><strong>No Redistribution:</strong> Users may not download, redistribute, or use video content outside of the Learniify platform.</p>
              </div>
            </CardContent>
          </Card>

          {/* Limitation of Liability */}
          <Card>
            <CardHeader>
              <CardTitle>Limitation of Liability</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3 text-sm">
                <p><strong>Content Availability:</strong> Learniify cannot guarantee the continued availability of any video content, as this depends on the original creators and YouTube's policies.</p>
                
                <p><strong>Third-Party Content:</strong> Learniify is not responsible for the accuracy, quality, or appropriateness of third-party video content.</p>
                
                <p><strong>Service Availability:</strong> While we strive for 100% uptime, Learniify cannot guarantee uninterrupted service availability.</p>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Contact & Legal Inquiries</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3 text-sm">
                <p><strong>General Inquiries:</strong> <EMAIL></p>
                <p><strong>Legal/DMCA:</strong> <EMAIL></p>
                <p><strong>Privacy Concerns:</strong> <EMAIL></p>
                
                <p className="text-xs text-gray-500 mt-4">
                  Last updated: {new Date().toLocaleDateString()}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="text-center mt-8">
          <Button onClick={() => setLocation('/dashboard')}>
            Back to Platform
          </Button>
        </div>
      </div>
    </div>
  );
}