import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Users, 
  Video, 
  BookOpen, 
  Activity, 
  TrendingUp, 
  Clock, 
  Shield,
  LogOut,
  Database,
  Globe,
  UserCheck,
  Zap,
  AlertTriangle,
  CheckCircle
} from "lucide-react";

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalVideos: number;
  totalPlans: number;
  totalProgress: number;
  recentUsers: Array<{
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    createdAt: string;
    lastActive: string;
  }>;
  topPlans: Array<{
    id: number;
    title: string;
    userCount: number;
    videoCount: number;
  }>;
  apiMetrics: {
    youtube: {
      totalCalls: number;
      todayCalls: number;
      errors: number;
      quotaExceeded: number;
      avgResponseTime: number;
      status: string;
      recentErrors: number;
    };
    openai: {
      totalCalls: number;
      todayCalls: number;
      errors: number;
      avgResponseTime: number;
      status: string;
      recentErrors: number;
    };
  };
  systemHealth: {
    dbStatus: string;
    apiStatus: string;
    uptime: string;
  };
  viewersByRegion: Array<{
    region: string;
    count: number;
  }>;
}

export default function AdminDashboard() {
  const [, setLocation] = useLocation();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showQuotaUpdate, setShowQuotaUpdate] = useState(false);
  const [quotaValue, setQuotaValue] = useState('');

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const { data: stats, isLoading } = useQuery<AdminStats>({
    queryKey: ["/api/admin/stats"],
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const handleLogout = async () => {
    try {
      await fetch("/api/admin/logout", {
        method: "POST",
        credentials: "include",
      });
      setLocation("/admin/login");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  const handleQuotaUpdate = async () => {
    try {
      const response = await fetch("/api/admin/quota/youtube", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({ quotaUsed: parseInt(quotaValue) })
      });
      
      if (response.ok) {
        setShowQuotaUpdate(false);
        setQuotaValue('');
        // Refresh stats
        window.location.reload();
      } else {
        alert('Failed to update quota');
      }
    } catch (error) {
      console.error('Error updating quota:', error);
      alert('Error updating quota');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-red-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <Shield className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-800">Admin Dashboard</h1>
                <p className="text-sm text-slate-600">Learniify Application Analytics</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm font-medium text-slate-800">
                  {currentTime.toLocaleDateString()}
                </p>
                <p className="text-xs text-slate-600">
                  {currentTime.toLocaleTimeString()}
                </p>
              </div>
              <Button
                onClick={async () => {
                  if (confirm('This will permanently delete all inactive plans. Are you sure?')) {
                    try {
                      const response = await fetch('/api/admin/cleanup', {
                        method: 'POST',
                        credentials: 'include'
                      });
                      const data = await response.json();
                      if (data.success) {
                        alert(`✅ ${data.message}`);
                        window.location.reload();
                      } else {
                        alert('❌ Cleanup failed');
                      }
                    } catch (error) {
                      alert('❌ Cleanup error');
                    }
                  }
                }}
                variant="outline"
                size="sm"
                className="text-orange-600 border-orange-200 hover:bg-orange-50 mr-2"
              >
                <Database className="w-4 h-4 mr-2" />
                Clean DB
              </Button>
              <Button
                onClick={handleLogout}
                variant="outline"
                size="sm"
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Total Users</p>
                  <p className="text-2xl font-bold text-slate-800">{stats?.totalUsers || 0}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Active Users</p>
                  <p className="text-2xl font-bold text-slate-800">{stats?.activeUsers || 0}</p>
                  <p className="text-xs text-slate-500 mt-1">Currently online</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <UserCheck className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Total Videos</p>
                  <p className="text-2xl font-bold text-slate-800">{stats?.totalVideos || 0}</p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <Video className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Learning Plans</p>
                  <p className="text-2xl font-bold text-slate-800">{stats?.totalPlans || 0}</p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                  <BookOpen className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Public Plans</p>
                  <p className="text-2xl font-bold text-slate-800">{stats?.publicPlans || 0}</p>
                  <p className="text-xs text-slate-500 mt-1">Publicly accessible</p>
                </div>
                <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center">
                  <Globe className="w-6 h-6 text-emerald-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Users */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="w-5 h-5 mr-2" />
                Recent Users
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats?.recentUsers?.slice(0, 5).map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                    <div>
                      <p className="font-medium text-slate-800">
                        {user.firstName} {user.lastName}
                      </p>
                      <p className="text-sm text-slate-600">{user.email}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-slate-500">
                        Joined: {new Date(user.createdAt).toLocaleDateString()}
                      </p>
                      <p className="text-xs text-slate-500">
                        Active: {new Date(user.lastActive).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )) || (
                  <p className="text-slate-500 text-center py-4">No recent users</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Top Learning Plans */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Popular Learning Plans
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats?.topPlans?.slice(0, 5).map((plan) => (
                  <div key={plan.id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                    <div>
                      <p className="font-medium text-slate-800">{plan.title}</p>
                      <p className="text-sm text-slate-600">{plan.videoCount} videos</p>
                    </div>
                    <Badge variant="secondary">
                      {plan.userCount} users
                    </Badge>
                  </div>
                )) || (
                  <p className="text-slate-500 text-center py-4">No plans available</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* API Metrics */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="w-5 h-5 mr-2" />
              API Usage Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* YouTube API */}
              <div className="p-4 border border-slate-200 rounded-lg">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-slate-800 flex items-center">
                    <Video className="w-4 h-4 mr-2" />
                    YouTube API
                  </h3>
                  <Badge className={`${
                    stats?.apiMetrics?.youtube?.status === 'Quota Exceeded' 
                      ? 'bg-red-100 text-red-800' 
                      : 'bg-green-100 text-green-800'
                  }`}>
                    {stats?.apiMetrics?.youtube?.status || 'Active'}
                  </Badge>
                </div>
                
                {/* Real Quota Data */}
                {stats?.apiMetrics?.youtube?.realQuota && (
                  <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-blue-800">YouTube API Quota Usage</h4>
                      <div className="flex items-center gap-2">
                        <Badge className={`${
                          stats.apiMetrics.youtube.realQuota.percentageUsed > 90 
                            ? 'bg-red-100 text-red-800'
                            : stats.apiMetrics.youtube.realQuota.percentageUsed > 70
                            ? 'bg-orange-100 text-orange-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {stats.apiMetrics.youtube.realQuota.percentageUsed}% Used
                        </Badge>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => setShowQuotaUpdate(true)}
                          className="text-xs"
                        >
                          Update
                        </Button>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-3 text-sm">
                      <div>
                        <p className="text-blue-600 font-medium">Used</p>
                        <p className="text-lg font-bold text-blue-800">
                          {stats.apiMetrics.youtube.realQuota.used.toLocaleString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-blue-600 font-medium">Remaining</p>
                        <p className="text-lg font-bold text-blue-800">
                          {stats.apiMetrics.youtube.realQuota.remaining.toLocaleString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-blue-600 font-medium">Limit</p>
                        <p className="text-lg font-bold text-blue-800">
                          {stats.apiMetrics.youtube.realQuota.limit.toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="mt-2 bg-white rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          stats.apiMetrics.youtube.realQuota.percentageUsed > 90 
                            ? 'bg-red-500'
                            : stats.apiMetrics.youtube.realQuota.percentageUsed > 70
                            ? 'bg-orange-500'
                            : 'bg-green-500'
                        }`}
                        style={{ width: `${stats.apiMetrics.youtube.realQuota.percentageUsed}%` }}
                      ></div>
                    </div>
                    {stats.apiMetrics.youtube.realQuota.lastUpdated && (
                      <p className="text-xs text-blue-600 mt-2">
                        Last updated: {new Date(stats.apiMetrics.youtube.realQuota.lastUpdated).toLocaleString()}
                      </p>
                    )}
                  </div>
                )}
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-slate-600">App Calls Today</p>
                    <p className="text-xl font-bold text-slate-800">
                      {stats?.apiMetrics?.youtube?.todayCalls || 0}
                    </p>
                  </div>
                  <div>
                    <p className="text-slate-600">Total App Calls</p>
                    <p className="text-xl font-bold text-slate-800">
                      {stats?.apiMetrics?.youtube?.totalCalls || 0}
                    </p>
                  </div>
                  <div>
                    <p className="text-slate-600">Errors</p>
                    <p className="text-lg font-semibold text-red-600">
                      {stats?.apiMetrics?.youtube?.errors || 0}
                    </p>
                  </div>
                  <div>
                    <p className="text-slate-600">Avg Response</p>
                    <p className="text-lg font-semibold text-blue-600">
                      {stats?.apiMetrics?.youtube?.avgResponseTime || 0}ms
                    </p>
                  </div>
                </div>
              </div>

              {/* OpenAI API */}
              <div className="p-4 border border-slate-200 rounded-lg">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-slate-800 flex items-center">
                    <Zap className="w-4 h-4 mr-2" />
                    OpenAI API
                  </h3>
                  <Badge className={`${
                    stats?.apiMetrics?.openai?.status === 'High Error Rate' 
                      ? 'bg-red-100 text-red-800' 
                      : 'bg-green-100 text-green-800'
                  }`}>
                    {stats?.apiMetrics?.openai?.status || 'Active'}
                  </Badge>
                </div>
                
                {/* Real Usage Data */}
                {stats?.apiMetrics?.openai?.realUsage && (
                  <div className="mb-4 p-3 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-green-800">Token Usage & Cost</h4>
                      <Badge className="bg-green-100 text-green-800">
                        ${stats.apiMetrics.openai.realUsage.estimatedCost.toFixed(4)}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-3 text-sm">
                      <div>
                        <p className="text-green-600 font-medium">Total Tokens</p>
                        <p className="text-lg font-bold text-green-800">
                          {stats.apiMetrics.openai.realUsage.tokensUsed.toLocaleString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-green-600 font-medium">Today's Tokens</p>
                        <p className="text-lg font-bold text-green-800">
                          {stats.apiMetrics.openai.realUsage.dailyTokens.toLocaleString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-green-600 font-medium">Est. Cost</p>
                        <p className="text-lg font-bold text-green-800">
                          ${stats.apiMetrics.openai.realUsage.estimatedCost.toFixed(4)}
                        </p>
                      </div>
                    </div>
                    {stats.apiMetrics.openai.realUsage.lastUpdated && (
                      <p className="text-xs text-green-600 mt-2">
                        Last updated: {new Date(stats.apiMetrics.openai.realUsage.lastUpdated).toLocaleString()}
                      </p>
                    )}
                  </div>
                )}
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-slate-600">App Calls Today</p>
                    <p className="text-xl font-bold text-slate-800">
                      {stats?.apiMetrics?.openai?.todayCalls || 0}
                    </p>
                  </div>
                  <div>
                    <p className="text-slate-600">Total App Calls</p>
                    <p className="text-xl font-bold text-slate-800">
                      {stats?.apiMetrics?.openai?.totalCalls || 0}
                    </p>
                  </div>
                  <div>
                    <p className="text-slate-600">Errors</p>
                    <p className="text-lg font-semibold text-red-600">
                      {stats?.apiMetrics?.openai?.errors || 0}
                    </p>
                  </div>
                  <div>
                    <p className="text-slate-600">Avg Response</p>
                    <p className="text-lg font-semibold text-blue-600">
                      {stats?.apiMetrics?.openai?.avgResponseTime || 0}ms
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
          {/* System Health */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="w-5 h-5 mr-2" />
                System Health
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4">
                <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div className="flex items-center">
                    <Database className="w-6 h-6 text-green-600 mr-3" />
                    <span className="font-medium text-slate-800">Database</span>
                  </div>
                  <Badge className="bg-green-100 text-green-800">
                    {stats?.systemHealth?.dbStatus || "Healthy"}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div className="flex items-center">
                    <Globe className="w-6 h-6 text-blue-600 mr-3" />
                    <span className="font-medium text-slate-800">API Status</span>
                  </div>
                  <Badge className="bg-blue-100 text-blue-800">
                    {stats?.systemHealth?.apiStatus || "Online"}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div className="flex items-center">
                    <Clock className="w-6 h-6 text-purple-600 mr-3" />
                    <span className="font-medium text-slate-800">Uptime</span>
                  </div>
                  <Badge className="bg-purple-100 text-purple-800">
                    {stats?.systemHealth?.uptime || "Unknown"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Viewers by Region */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Globe className="w-5 h-5 mr-2" />
                Viewers by Region
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats?.viewersByRegion?.length > 0 ? (
                  stats.viewersByRegion.map((region, index) => (
                    <div key={region.region} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                          <span className="text-xs font-bold text-blue-600">#{index + 1}</span>
                        </div>
                        <span className="font-medium text-slate-800">{region.region}</span>
                      </div>
                      <Badge variant="secondary">
                        {region.count} users
                      </Badge>
                    </div>
                  ))
                ) : (
                  <p className="text-slate-500 text-center py-4">No regional data available</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Quota Update Modal */}
      {showQuotaUpdate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-[90vw]">
            <h3 className="text-lg font-semibold mb-4">Update YouTube API Quota</h3>
            <p className="text-sm text-gray-600 mb-4">
              Enter the current quota usage from Google Cloud Console.
              Current usage shown: {stats?.apiMetrics?.youtube?.realQuota?.used || 0} / 10,000
            </p>
            <input
              type="number"
              placeholder="Enter quota used (e.g., 9388)"
              value={quotaValue}
              onChange={(e) => setQuotaValue(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded mb-4"
              min="0"
              max="10000"
            />
            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  setShowQuotaUpdate(false);
                  setQuotaValue('');
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleQuotaUpdate}
                disabled={!quotaValue || parseInt(quotaValue) < 0 || parseInt(quotaValue) > 10000}
              >
                Update Quota
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}