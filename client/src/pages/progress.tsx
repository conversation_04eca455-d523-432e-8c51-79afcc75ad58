import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress as ProgressBar } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Link } from "wouter";
import { 
  TrendingUp, 
  Calendar, 
  Trophy, 
  Clock, 
  CheckCircle2,
  PlayCircle,
  Target,
  Award,
  BookOpen,
  BarChart3
} from "lucide-react";

interface VideoProgress {
  id: number;
  userId: string;
  videoId: number;
  currentTime: number;
  isCompleted: boolean;
  lastWatched: string;
  video: {
    id: number;
    youtubeId: string;
    title: string;
    description: string;
    thumbnailUrl: string;
    duration: string;
    channelTitle: string;
  };
}

interface WeeklyProgress {
  totalHours: number;
  completedVideos: number;
  dailyHours: number[];
}

interface DailyStats {
  videosWatched: number;
  timeSpent: number;
  streakDays: number;
}

export default function Progress() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  const { data: weeklyProgress } = useQuery({
    queryKey: ['/api/analytics/weekly'],
    enabled: isAuthenticated,
  });

  const { data: dailyStats } = useQuery({
    queryKey: ['/api/analytics/daily'],
    enabled: isAuthenticated,
  });

  const { data: continueVideos = [] } = useQuery({
    queryKey: ['/api/continue-learning'],
    enabled: isAuthenticated,
  });

  const { data: achievements = [] } = useQuery({
    queryKey: ['/api/achievements'],
    enabled: isAuthenticated,
  });

  const { data: monthlyStats } = useQuery({
    queryKey: ['/api/analytics/monthly'],
    enabled: isAuthenticated,
  });

  if (authLoading) {
    return (
      <div className="min-h-screen bg-slate-50">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-slate-600">Loading your progress...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-slate-50">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-slate-800 mb-2">Please Sign In</h2>
            <p className="text-slate-600 mb-4">You need to be signed in to view your progress.</p>
            <Button onClick={() => window.location.href = '/api/login'}>
              Sign In
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  const currentWeekHours = weeklyProgress?.dailyHours || [0, 0, 0, 0, 0, 0, 0];
  const maxHours = Math.max(...currentWeekHours, 1);

  return (
    <div className="min-h-screen bg-slate-50">
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-800 mb-2">Learning Progress</h1>
          <p className="text-slate-600">Track your learning journey and achievements</p>
        </div>

        {/* Quick Stats */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <CheckCircle2 className="w-6 h-6 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-slate-800">
                {dailyStats?.videosWatched || 0}
              </div>
              <div className="text-sm text-slate-600">Videos Today</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Clock className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-slate-800">
                {Math.round((dailyStats?.timeSpent || 0) / 60)}m
              </div>
              <div className="text-sm text-slate-600">Time Today</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Target className="w-6 h-6 text-orange-600" />
              </div>
              <div className="text-2xl font-bold text-slate-800">
                {dailyStats?.streakDays || 0}
              </div>
              <div className="text-sm text-slate-600">Day Streak</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Trophy className="w-6 h-6 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-slate-800">
                {achievements.length}
              </div>
              <div className="text-sm text-slate-600">Achievements</div>
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Progress Section */}
          <div className="lg:col-span-2 space-y-6">
            {/* This Week's Progress */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  This Week's Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-600">Total Hours</span>
                    <span className="font-medium">{weeklyProgress?.totalHours || 0}h</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-600">Videos Completed</span>
                    <span className="font-medium">{weeklyProgress?.completedVideos || 0}</span>
                  </div>
                  
                  {/* Daily Hours Chart */}
                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-slate-700 mb-3">Daily Activity</h4>
                    <div className="flex items-end space-x-2 h-24">
                      {weekDays.map((day, index) => (
                        <div key={day} className="flex-1 flex flex-col items-center">
                          <div className="w-full bg-slate-200 rounded-sm flex-grow flex items-end">
                            <div
                              className="w-full bg-blue-500 rounded-sm min-h-[4px]"
                              style={{
                                height: `${(currentWeekHours[index] / maxHours) * 100}%`,
                              }}
                            />
                          </div>
                          <span className="text-xs text-slate-500 mt-1">{day}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Continue Learning */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PlayCircle className="w-5 h-5 mr-2" />
                  Continue Learning
                </CardTitle>
              </CardHeader>
              <CardContent>
                {continueVideos.length === 0 ? (
                  <div className="text-center py-8">
                    <BookOpen className="w-12 h-12 text-slate-300 mx-auto mb-3" />
                    <p className="text-slate-600">No videos in progress</p>
                    <Link href="/explore">
                      <Button className="mt-3 bg-blue-500 hover:bg-blue-600 text-white">
                        Explore Content
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {continueVideos.slice(0, 3).filter((videoProgress: any) => videoProgress && videoProgress.youtubeId).map((videoProgress: any) => (
                      <div key={videoProgress.id} className="flex items-center space-x-4 p-3 bg-slate-50 rounded-lg">
                        <img
                          src={videoProgress.thumbnailUrl || '/api/placeholder/600/300'}
                          alt={videoProgress.title}
                          className="w-16 h-12 rounded object-cover"
                        />
                        <div className="flex-grow">
                          <h4 className="font-medium text-slate-800 line-clamp-1">
                            {videoProgress.title}
                          </h4>
                          <p className="text-sm text-slate-600">
                            {videoProgress.channelTitle}
                          </p>
                          <div className="mt-2">
                            <ProgressBar value={videoProgress.progress?.progressPercentage || 0} className="h-1" />
                            <span className="text-xs text-slate-500">{videoProgress.progress?.progressPercentage || 0}% complete</span>
                          </div>
                        </div>
                        <Link href={`/video/${videoProgress.youtubeId}`}>
                          <Button size="sm" className="bg-blue-500 hover:bg-blue-600 text-white">
                            Continue
                          </Button>
                        </Link>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Monthly Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Monthly Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-slate-600">Hours Learned</span>
                    <span className="font-semibold">{monthlyStats?.totalHours || 0}h</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Videos Completed</span>
                    <span className="font-semibold">{monthlyStats?.totalVideos || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Learning Plans</span>
                    <span className="font-semibold">{monthlyStats?.completedPlans || 0}</span>
                  </div>
                  
                  {monthlyStats?.topCategories && monthlyStats.topCategories.length > 0 && (
                    <div className="mt-4">
                      <div className="text-sm text-slate-600 mb-2">Top Categories:</div>
                      <div className="flex flex-wrap gap-1">
                        {monthlyStats.topCategories.slice(0, 2).map((category, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {category}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {(monthlyStats?.totalVideos || 0) > 0 && (
                    <div className="mt-4 p-3 bg-green-50 rounded-lg text-center">
                      <TrendingUp className="w-8 h-8 text-green-600 mx-auto mb-2" />
                      <p className="text-sm text-green-700 font-medium">
                        {monthlyStats?.learningStreak || 0} day learning streak!
                      </p>
                      <p className="text-xs text-green-600">
                        Avg. {monthlyStats?.averageSessionTime || 0}h per video
                      </p>
                    </div>
                  )}
                  
                  {(monthlyStats?.totalVideos || 0) === 0 && (
                    <div className="mt-4 p-3 bg-slate-50 rounded-lg text-center">
                      <BookOpen className="w-8 h-8 text-slate-400 mx-auto mb-2" />
                      <p className="text-sm text-slate-600 font-medium">Start Your Learning Journey</p>
                      <p className="text-xs text-slate-500">Complete your first video this month</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Recent Achievements */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="w-5 h-5 mr-2" />
                  Recent Achievements
                </CardTitle>
              </CardHeader>
              <CardContent>
                {achievements.length === 0 ? (
                  <div className="text-center py-4">
                    <Trophy className="w-8 h-8 text-slate-300 mx-auto mb-2" />
                    <p className="text-sm text-slate-600">No achievements yet</p>
                    <p className="text-xs text-slate-500">Keep learning to earn badges!</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {achievements.slice(0, 3).map((achievement: any) => (
                      <div key={achievement.id} className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                          <Trophy className="w-4 h-4 text-yellow-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-slate-800">
                            {achievement.title}
                          </p>
                          <p className="text-xs text-slate-600">
                            {new Date(achievement.earnedAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Learning Goal */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="w-5 h-5 mr-2" />
                  Weekly Goal
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-2xl font-bold text-slate-800 mb-2">
                    {Math.round(((weeklyProgress?.totalHours || 0) / 10) * 100)}%
                  </div>
                  <ProgressBar value={((weeklyProgress?.totalHours || 0) / 10) * 100} className="mb-3" />
                  <p className="text-sm text-slate-600">
                    {weeklyProgress?.totalHours || 0} / 10 hours this week
                  </p>
                  <p className="text-xs text-slate-500 mt-2">
                    {10 - (weeklyProgress?.totalHours || 0)} hours remaining
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}