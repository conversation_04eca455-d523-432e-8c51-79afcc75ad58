import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON> } from "wouter";
import { ArrowLeft, HelpCircle, ChevronDown, ChevronRight } from "lucide-react";
import { useState } from "react";

const faqs = [
  {
    category: "Getting Started",
    questions: [
      {
        question: "How do I create an account?",
        answer: "You can create an account by clicking the 'Sign Up' button and using either your email or Google account. It's completely free to get started."
      },
      {
        question: "How do I add videos to my library?",
        answer: "You can add videos by searching for them in the Video Search section on your dashboard, or by browsing our curated playlists in the Library section."
      },
      {
        question: "What is a Learning Plan?",
        answer: "A Learning Plan is a structured collection of videos organized around a specific topic or skill. You can create custom plans or use our AI-recommended paths."
      }
    ]
  },
  {
    category: "Features",
    questions: [
      {
        question: "How does progress tracking work?",
        answer: "We automatically track your viewing progress, completion status, and learning patterns. You can view detailed analytics in your Profile section."
      },
      {
        question: "Can I share my Learning Plans?",
        answer: "Yes! You can generate shareable links for your Learning Plans, allowing friends and colleagues to access and copy your curated content."
      },
      {
        question: "What are AI Recommendations?",
        answer: "Our AI analyzes your viewing history, preferences, and learning patterns to suggest personalized content that matches your interests and skill level."
      }
    ]
  },
  {
    category: "Account & Privacy",
    questions: [
      {
        question: "Is my data secure?",
        answer: "Yes, we use industry-standard encryption and security measures. We never share your personal data with third parties without your consent."
      },
      {
        question: "Can I delete my account?",
        answer: "Yes, you can delete your account anytime from your Profile settings. This will permanently remove all your data from our servers."
      },
      {
        question: "How do you use cookies?",
        answer: "We use cookies to enhance your experience, remember your preferences, and provide personalized recommendations. See our Cookie Policy for details."
      }
    ]
  },
  {
    category: "Technical Support",
    questions: [
      {
        question: "Videos won't play, what should I do?",
        answer: "Try refreshing the page, clearing your browser cache, or checking your internet connection. If issues persist, contact our support team."
      },
      {
        question: "Is Learnify mobile-friendly?",
        answer: "Yes! Learnify is fully responsive and works great on all devices - desktop, tablet, and mobile phones."
      },
      {
        question: "Which browsers are supported?",
        answer: "We support all modern browsers including Chrome, Firefox, Safari, and Edge. For the best experience, keep your browser updated."
      }
    ]
  }
];

function FAQItem({ question, answer }: { question: string; answer: string }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border border-gray-200 rounded-lg">
      <button
        className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="font-medium text-gray-900">{question}</span>
        {isOpen ? (
          <ChevronDown className="w-5 h-5 text-gray-500" />
        ) : (
          <ChevronRight className="w-5 h-5 text-gray-500" />
        )}
      </button>
      {isOpen && (
        <div className="px-4 pb-3 text-gray-600 border-t border-gray-100">
          <p className="pt-3">{answer}</p>
        </div>
      )}
    </div>
  );
}

export default function FAQ() {
  return (
    <div className="min-h-screen bg-slate-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="outline" size="sm" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
          <div className="flex items-center gap-3 mb-4">
            <HelpCircle className="w-8 h-8 text-blue-500" />
            <h1 className="text-3xl font-bold text-gray-900">
              Frequently Asked Questions
            </h1>
          </div>
          <p className="text-gray-600">
            Find answers to common questions about Learnify. Can't find what you're looking for? 
            <Link href="/contact" className="text-blue-600 hover:underline ml-1">
              Contact our support team
            </Link>.
          </p>
        </div>

        {/* FAQ Categories */}
        <div className="space-y-8">
          {faqs.map((category, categoryIndex) => (
            <Card key={categoryIndex}>
              <CardHeader>
                <CardTitle className="text-xl text-gray-800">
                  {category.category}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {category.questions.map((faq, faqIndex) => (
                    <FAQItem
                      key={faqIndex}
                      question={faq.question}
                      answer={faq.answer}
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Contact Section */}
        <Card className="mt-8 bg-blue-50 border-blue-200">
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              Still have questions?
            </h3>
            <p className="text-blue-700 mb-4">
              Our support team is here to help you get the most out of Learnify.
            </p>
            <Link href="/contact">
              <Button className="bg-blue-600 hover:bg-blue-700">
                Contact Support
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}