import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import FriendsNetwork from "@/components/friends-network";
import LearningPathVisualizer from "@/components/learning-path-visualizer";
import DifficultyOptimizer from "@/components/difficulty-optimizer";
import CommonPlaylist from "@/components/common-playlist";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  Play,
  Plus,
  Trash2,
  BookO<PERSON>,
  Users,
  Target,
  Brain,
  List,
  History,
  Clock,
  Eye,
  Heart
} from "lucide-react";
import { useState } from "react";
import { Link } from "wouter";
import VideoCard from "@/components/video-card";

// Favorites Section Component
function FavoritesSection() {
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<'videos' | 'playlists'>('videos');

  const { data: favoriteVideos = [] } = useQuery({
    queryKey: ['/api/favorites/videos'],
    enabled: isAuthenticated,
  });

  const { data: favoritePlaylists = [] } = useQuery({
    queryKey: ['/api/favorites/playlists'],
    enabled: isAuthenticated,
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Heart className="w-6 h-6 text-red-500" />
        <h2 className="text-2xl font-bold text-slate-800">My Favorites</h2>
      </div>

      <div className="flex space-x-1 bg-slate-100 p-1 rounded-lg w-fit">
        <button
          onClick={() => setActiveTab('videos')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'videos'
              ? 'bg-white text-slate-900 shadow-sm'
              : 'text-slate-600 hover:text-slate-900'
          }`}
        >
          Videos ({favoriteVideos.length})
        </button>
        <button
          onClick={() => setActiveTab('playlists')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'playlists'
              ? 'bg-white text-slate-900 shadow-sm'
              : 'text-slate-600 hover:text-slate-900'
          }`}
        >
          Playlists ({favoritePlaylists.length})
        </button>
      </div>

      {activeTab === 'videos' && (
        favoriteVideos.length === 0 ? (
          <Card className="p-12 text-center">
            <CardContent>
              <Heart className="w-16 h-16 mx-auto mb-4 text-slate-400" />
              <h3 className="text-xl font-semibold mb-2">No Favorite Videos</h3>
              <p className="text-slate-600 mb-4">
                Videos you mark as favorites will appear here.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {favoriteVideos.map((favorite: any) => (
              <VideoCard
                key={favorite.id}
                video={favorite.video}
                showProgress={false}
                showFavoriteButton={true}
              />
            ))}
          </div>
        )
      )}

      {activeTab === 'playlists' && (
        favoritePlaylists.length === 0 ? (
          <Card className="p-12 text-center">
            <CardContent>
              <Heart className="w-16 h-16 mx-auto mb-4 text-slate-400" />
              <h3 className="text-xl font-semibold mb-2">No Favorite Playlists</h3>
              <p className="text-slate-600 mb-4">
                Learning plans you mark as favorites will appear here.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {favoritePlaylists.map((favorite: any) => (
              <Card key={favorite.id} className="group hover:shadow-lg transition-all duration-200">
                <CardContent className="p-6">
                  <h3 className="font-semibold text-slate-800 mb-2 line-clamp-2">
                    {favorite.playlist.title}
                  </h3>
                  <p className="text-sm text-slate-600 line-clamp-3 mb-4">
                    {favorite.playlist.description || 'No description available'}
                  </p>
                  <Button
                    onClick={() => window.location.href = `/plan/${favorite.playlist.slug}`}
                    size="sm"
                    className="w-full"
                  >
                    <BookOpen className="w-4 h-4 mr-2" />
                    View Plan
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        )
      )}
    </div>
  );
}

// Recent Viewing History Component
function RecentViewingHistory() {
  const { isAuthenticated } = useAuth();
  
  const { data: viewingHistory = [], isLoading } = useQuery({
    queryKey: ['/api/user/viewing-history'],
    enabled: isAuthenticated,
  });

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 mb-4">
            <History className="w-5 h-5 text-slate-600" />
            <h3 className="text-lg font-semibold">Recent Viewing History</h3>
          </div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex space-x-3 animate-pulse">
                <div className="w-20 h-12 bg-slate-200 rounded"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                  <div className="h-3 bg-slate-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (viewingHistory.length === 0) {
    return null;
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <History className="w-5 h-5 text-slate-600" />
            <h3 className="text-lg font-semibold">Recent Viewing History</h3>
            <Badge variant="secondary" className="text-xs">
              Last 10
            </Badge>
          </div>
          <Link href="/profile">
            <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
              View All
            </Button>
          </Link>
        </div>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
          {viewingHistory.slice(0, 10).map((item: any) => (
            <Link key={item.id} href={`/video/${item.video.youtubeId}`}>
              <div className="group cursor-pointer">
                <div className="relative">
                  <img
                    src={item.video.thumbnailUrl}
                    alt={item.video.title}
                    className="w-full h-16 sm:h-20 object-cover rounded-lg group-hover:opacity-90 transition-opacity"
                  />
                  {item.isCompleted ? (
                    <div className="absolute top-1 right-1 bg-green-500 text-white rounded-full p-1">
                      <Play className="w-2 h-2 fill-current" />
                    </div>
                  ) : item.currentTime > 0 && (
                    <div className="absolute bottom-1 left-1 right-1">
                      <div className="w-full bg-black/50 rounded-full h-1">
                        <div 
                          className="bg-blue-500 h-1 rounded-full"
                          style={{ width: `${Math.min((item.currentTime / 100) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                  <div className="absolute bottom-1 right-1 bg-black/75 text-white text-xs px-1 py-0.5 rounded">
                    {item.video.duration || 'N/A'}
                  </div>
                </div>
                <div className="mt-2">
                  <h4 className="text-xs sm:text-sm font-medium line-clamp-2 group-hover:text-blue-600 transition-colors">
                    {item.video.title}
                  </h4>
                  <p className="text-xs text-slate-500 mt-1 line-clamp-1">
                    {item.video.channelTitle}
                  </p>
                  <div className="flex items-center gap-1 mt-1 text-xs text-slate-400">
                    <Clock className="w-3 h-3" />
                    <span className="hidden sm:inline">{new Date(item.lastWatched).toLocaleDateString()}</span>
                    <span className="sm:hidden">{new Date(item.lastWatched).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

interface Video {
  id: number;
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  duration: string;
  channelTitle: string;
  publishedAt: string;
  viewCount: number;
}

interface LearningPlan {
  id: number;
  title: string;
  description: string;
}

export default function Library() {
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [isAddToPlanOpen, setIsAddToPlanOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>("");

  // Get all videos from library
  const { data: videos = [], isLoading } = useQuery({
    queryKey: ['/api/videos'],
    enabled: isAuthenticated,
  });

  // Get user's learning plans for the "Add to Plan" functionality
  const { data: userPlans = [] } = useQuery({
    queryKey: ['/api/learning-plans'],
    enabled: isAuthenticated,
  });

  // Filter videos based on search query
  const filteredVideos = videos.filter((video: Video) =>
    video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    video.channelTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
    video.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const addToPlanMutation = useMutation({
    mutationFn: async ({ video, planId }: { video: Video; planId: string }) => {
      return await apiRequest(`/api/learning-plans/${planId}/videos`, {
        method: "POST",
        body: JSON.stringify({ videoId: video.id })
      });
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['/api/learning-plans'] });
      queryClient.invalidateQueries({ queryKey: [`/api/learning-plans/${variables.planId}/videos`] });
      setIsAddToPlanOpen(false);
      setSelectedVideo(null);
      setSelectedPlan("");
      toast({
        title: "Added to Plan",
        description: "Video has been added to your learning plan",
      });
    },
    onError: (error) => {
      console.error("Error adding video to plan:", error);
      toast({
        title: "Error",
        description: "Failed to add video to plan",
        variant: "destructive",
      });
    },
  });

  const deleteVideoMutation = useMutation({
    mutationFn: async (videoId: number) => {
      return await apiRequest(`/api/videos/${videoId}`, {
        method: "DELETE"
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/videos'] });
      toast({
        title: "Video Removed",
        description: "Video has been removed from your library",
      });
    },
    onError: (error) => {
      console.error("Error deleting video:", error);
      toast({
        title: "Error",
        description: "Failed to remove video from library",
        variant: "destructive",
      });
    },
  });

  const handleAddToPlan = (video: Video) => {
    // If user has no plans, show error
    if (!userPlans || userPlans.length === 0) {
      toast({
        title: "No Learning Plans",
        description: "Create a learning plan first to add videos.",
        variant: "destructive",
      });
      return;
    }

    // If user has only one plan, add directly
    if (userPlans.length === 1) {
      addToPlanMutation.mutate({ video, planId: userPlans[0].id.toString() });
      return;
    }

    // If user has multiple plans, show selection dialog
    setSelectedVideo(video);
    setIsAddToPlanOpen(true);
  };

  const handleConfirmAddToPlan = () => {
    if (selectedVideo && selectedPlan) {
      addToPlanMutation.mutate({ video: selectedVideo, planId: selectedPlan });
    }
  };

  const handlePlayVideo = (youtubeId: string) => {
    window.location.href = `/video/${youtubeId}`;
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <Card className="p-8 text-center">
          <CardContent>
            <BookOpen className="w-12 h-12 mx-auto mb-4 text-slate-400" />
            <h2 className="text-xl font-semibold mb-2">Sign In Required</h2>
            <p className="text-slate-600 mb-4">Please sign in to view your video library.</p>
            <Button onClick={() => window.location.href = '/api/login'}>
              Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50">
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-800 mb-4 flex items-center">
            <BookOpen className="w-8 h-8 mr-3" />
            Learning Hub
          </h1>
          <p className="text-slate-600 mb-6">
            Manage your videos, connect with friends, and visualize your learning journey
          </p>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="library" className="w-full">
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 mb-8 h-auto">
            <TabsTrigger value="library" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm p-2 sm:p-3">
              <BookOpen className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden xs:inline">Video </span>Library
            </TabsTrigger>
            <TabsTrigger value="favorites" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm p-2 sm:p-3">
              <Heart className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden xs:inline">My </span>Favorites
            </TabsTrigger>
            <TabsTrigger value="playlist" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm p-2 sm:p-3">
              <List className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden xs:inline">Common </span>Playlist
            </TabsTrigger>
            <TabsTrigger value="friends" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm p-2 sm:p-3">
              <Users className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Friends </span>Network
            </TabsTrigger>
            <TabsTrigger value="paths" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm p-2 sm:p-3">
              <Target className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Learning </span>Paths
            </TabsTrigger>
            <TabsTrigger value="difficulty" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm p-2 sm:p-3">
              <Brain className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">AI </span>Optimizer
            </TabsTrigger>
          </TabsList>

          <TabsContent value="library" className="space-y-6">
            {/* Search Bar */}
            <div className="relative max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="Search your library..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>



            {/* Loading State */}
            {isLoading && (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                  <Card key={i} className="animate-pulse">
                    <CardContent className="p-4">
                      <div className="bg-slate-200 h-40 rounded-md mb-3"></div>
                      <div className="bg-slate-200 h-4 rounded mb-2"></div>
                      <div className="bg-slate-200 h-3 rounded mb-4"></div>
                      <div className="bg-slate-200 h-8 rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Empty State */}
            {!isLoading && filteredVideos.length === 0 && (
              <Card className="p-12 text-center">
                <CardContent>
                  <BookOpen className="w-16 h-16 mx-auto mb-4 text-slate-400" />
                  <h2 className="text-xl font-semibold mb-2">
                    {searchQuery ? 'No videos found' : 'Your library is empty'}
                  </h2>
                  <p className="text-slate-600 mb-4">
                    {searchQuery 
                      ? 'Try searching with different keywords or clear your search.'
                      : 'Start exploring and adding videos to build your learning library.'
                    }
                  </p>
                  {!searchQuery && (
                    <Button onClick={() => window.location.href = '/explore'}>
                      Explore Videos
                    </Button>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Videos Grid */}
            {!isLoading && filteredVideos.length > 0 && (
              <>
                <div className="mb-4 text-sm text-slate-600">
                  {filteredVideos.length} video{filteredVideos.length !== 1 ? 's' : ''} in your library
                </div>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {filteredVideos.map((video: Video) => (
                    <VideoCard
                      key={video.id}
                      video={video}
                      showProgress={false}
                      showFavoriteButton={true}
                    />
                  ))}
                </div>
              </>
            )}
          </TabsContent>

          <TabsContent value="favorites" className="space-y-6">
            <FavoritesSection />
          </TabsContent>

          <TabsContent value="playlist" className="space-y-6">
            <CommonPlaylist />
          </TabsContent>

          <TabsContent value="friends" className="space-y-6">
            <FriendsNetwork />
          </TabsContent>

          <TabsContent value="paths" className="space-y-6">
            <div className="space-y-6">
              {userPlans && userPlans.length > 0 ? (
                userPlans.map((plan: LearningPlan) => (
                  <LearningPathVisualizer 
                    key={plan.id} 
                    planId={plan.id} 
                    planTitle={plan.title} 
                  />
                ))
              ) : (
                <Card className="p-12 text-center">
                  <CardContent>
                    <Target className="w-16 h-16 mx-auto mb-4 text-slate-400" />
                    <h2 className="text-xl font-semibold mb-2">No Learning Plans</h2>
                    <p className="text-slate-600 mb-4">
                      Create learning plans to visualize your learning paths and track progress.
                    </p>
                    <Button onClick={() => window.location.href = '/my-plans'}>
                      Create Learning Plan
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="difficulty" className="space-y-6">
            <DifficultyOptimizer />
          </TabsContent>
        </Tabs>

        {/* Plan Selection Dialog */}
        <Dialog open={isAddToPlanOpen} onOpenChange={setIsAddToPlanOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add to Learning Plan</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p className="text-sm text-slate-600">
                Select a learning plan to add "{selectedVideo?.title}" to:
              </p>
              <Select value={selectedPlan} onValueChange={setSelectedPlan}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a learning plan" />
                </SelectTrigger>
                <SelectContent>
                  {userPlans.map((plan: any) => (
                    <SelectItem key={plan.id} value={plan.id.toString()}>
                      {plan.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="flex gap-3">
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => setIsAddToPlanOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  className="flex-1"
                  onClick={handleConfirmAddToPlan}
                  disabled={!selectedPlan || addToPlanMutation.isPending}
                >
                  {addToPlanMutation.isPending ? "Adding..." : "Add to Plan"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}