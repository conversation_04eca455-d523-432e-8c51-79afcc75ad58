import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "wouter";
import { <PERSON>L<PERSON>t, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";

export default function CookiePolicy() {
  return (
    <div className="min-h-screen bg-slate-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="outline" size="sm" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
          <div className="flex items-center gap-3 mb-4">
            <Cookie className="w-8 h-8 text-orange-500" />
            <h1 className="text-3xl font-bold text-gray-900"><PERSON><PERSON> Policy</h1>
          </div>
          <p className="text-gray-600">
            Effective Date: January 1, 2025 | Last Updated: {new Date().toLocaleDateString()}
          </p>
        </div>

        {/* Introduction */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-blue-500" />
              What are Cookies?
            </CardTitle>
          </CardHeader>
          <CardContent className="prose prose-gray max-w-none">
            <p>
              Cookies are small text files that are stored on your device when you visit our website. 
              They help us provide you with a better experience by remembering your preferences and 
              understanding how you use Learniify.
            </p>
          </CardContent>
        </Card>

        {/* Types of Cookies */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5 text-green-500" />
              Types of Cookies We Use
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Essential Cookies</h3>
                <p className="text-gray-600 mb-2">
                  These cookies are necessary for the website to function properly. They enable core functionality 
                  such as security, network management, and accessibility.
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  <li><strong>connect.sid</strong> - Session authentication cookie (HttpOnly, 30 minutes)</li>
                  <li>Security and CSRF protection</li>
                  <li>Load balancing and performance optimization</li>
                  <li>User authentication and login sessions</li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Functional Cookies</h3>
                <p className="text-gray-600 mb-2">
                  These cookies enhance your experience by remembering your preferences and settings.
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  <li>Theme preferences (light/dark mode)</li>
                  <li>Video player settings and preferences</li>
                  <li>Learning plan view preferences</li>
                  <li>User interface customizations</li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Analytics Cookies</h3>
                <p className="text-gray-600 mb-2">
                  We currently do not use third-party analytics cookies. All usage analytics are processed 
                  server-side without storing tracking cookies on your device.
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  <li>No Google Analytics or similar tracking</li>
                  <li>Server-side activity logging only</li>
                  <li>API usage metrics (server-side only)</li>
                  <li>Performance monitoring (no client cookies)</li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Personalization</h3>
                <p className="text-gray-600 mb-2">
                  Personalization data is stored in our secure database, not in cookies. This includes:
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  <li>Learning progress and video completion status</li>
                  <li>Personal learning plans and organization</li>
                  <li>Video viewing history and preferences</li>
                  <li>Friend connections and shared content</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Third-Party Cookies */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart className="w-5 h-5 text-purple-500" />
              Third-Party Services
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              We use trusted third-party services that may set their own cookies:
            </p>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900">Google OAuth</h4>
                <p className="text-gray-600 text-sm">
                  When you sign in with Google, Google may set authentication cookies according to their privacy policy. 
                  <a href="https://policies.google.com/privacy" target="_blank" rel="noopener noreferrer" 
                     className="text-blue-600 hover:underline ml-1">
                    Google Privacy Policy
                  </a>
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">YouTube Embedded Videos</h4>
                <p className="text-gray-600 text-sm">
                  YouTube embedded videos may set cookies for video playback and preferences. We use YouTube's privacy-enhanced mode when possible.
                  <a href="https://www.youtube.com/t/terms" target="_blank" rel="noopener noreferrer" 
                     className="text-blue-600 hover:underline ml-1">
                    YouTube Terms
                  </a>
                </p>
              </div>

            </div>
          </CardContent>
        </Card>

        {/* Managing Cookies */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Managing Your Cookie Preferences</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Browser Settings</h4>
                <p className="text-gray-600 mb-2">
                  You can control cookies through your browser settings:
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  <li>Block all cookies</li>
                  <li>Block third-party cookies only</li>
                  <li>Delete existing cookies</li>
                  <li>Set preferences for specific websites</li>
                </ul>
              </div>
              
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p className="text-yellow-800 text-sm">
                  <strong>Note:</strong> Disabling essential cookies will prevent you from logging into Learniify 
                  and accessing your learning progress. Session cookies are required for authentication and security.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <h3 className="font-semibold text-blue-900 mb-2">Questions About Our Cookie Policy?</h3>
            <p className="text-blue-700 mb-4">
              If you have any questions about how we use cookies or this policy, please contact us.
            </p>
            <div className="flex gap-3">
              <Link href="/contact">
                <Button variant="outline" className="border-blue-300 text-blue-700 hover:bg-blue-100">
                  Contact Us
                </Button>
              </Link>
              <Link href="/privacy">
                <Button variant="outline" className="border-blue-300 text-blue-700 hover:bg-blue-100">
                  Privacy Policy
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}