import { useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useLocation } from "wouter";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Heart, 
  Users, 
  Globe, 
  Zap, 
  Coffee, 
  Gift,
  Star,
  CheckCircle,
  ArrowRight,
  DollarSign
} from "lucide-react";

const DONATION_TIERS = [
  {
    amount: 5,
    title: "Coffee Supporter",
    icon: Coffee,
    description: "Buy us a coffee to keep the servers running",
    benefits: ["Support free education", "Feel good about helping others"],
    popular: false,
  },
  {
    amount: 15,
    title: "Learning Advocate",
    icon: Heart,
    description: "Help us reach more learners worldwide",
    benefits: ["Support 10+ learners", "Priority feature requests", "Supporter badge"],
    popular: true,
  },
  {
    amount: 50,
    title: "Education Champion",
    icon: Star,
    description: "Make a significant impact on global education",
    benefits: ["Support 50+ learners", "Early access to features", "Special recognition"],
    popular: false,
  },
  {
    amount: 100,
    title: "Learning Hero",
    icon: Gift,
    description: "Transform education for hundreds of learners",
    benefits: ["Support 100+ learners", "Direct feedback channel", "Hero status"],
    popular: false,
  },
];

export default function DonatePage() {
  const [selectedAmount, setSelectedAmount] = useState(15);
  const [customAmount, setCustomAmount] = useState("");
  const [isCustom, setIsCustom] = useState(false);
  const [, setLocation] = useLocation();

  const handleDonate = async (amount: number) => {
    // Mark user as supporter after donation
    try {
      await fetch('/api/user/supporter', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isSupporter: true }),
      });
      
      // Redirect to PayPal or payment processor
      window.open(`https://www.paypal.com/donate/?hosted_button_id=YOUR_PAYPAL_BUTTON_ID&amount=${amount}`, '_blank');
      
      // Show success message
      setTimeout(() => {
        alert('Thank you for your support! You now have ad-free access.');
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error('Error updating supporter status:', error);
      // Still proceed with donation
      window.open(`https://www.paypal.com/donate/?hosted_button_id=YOUR_PAYPAL_BUTTON_ID&amount=${amount}`, '_blank');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <img 
                src="https://learniify-logo.s3.ap-south-1.amazonaws.com/logo.png" 
                alt="Learniify Logo" 
                className="h-10 w-auto object-contain"
              />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Support Learniify</h1>
                <p className="text-gray-600">Keep education free for everyone</p>
              </div>
            </div>
            <Button variant="outline" onClick={() => setLocation('/dashboard')}>
              ← Back to Learning
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-12">
        {/* Mission Statement */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Heart className="w-4 h-4" />
            100% Free Education Platform
          </div>
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Help Us Keep Learning Free for Everyone
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Learniify is committed to providing free, high-quality education to learners worldwide. 
            Your support helps us maintain servers, develop new features, and reach more students who need it most.
          </p>
        </div>

        {/* Impact Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <Card className="text-center">
            <CardContent className="p-6">
              <Users className="w-8 h-8 text-blue-600 mx-auto mb-3" />
              <div className="text-2xl font-bold text-gray-900">10,000+</div>
              <div className="text-gray-600">Active Learners</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-6">
              <Globe className="w-8 h-8 text-green-600 mx-auto mb-3" />
              <div className="text-2xl font-bold text-gray-900">50+</div>
              <div className="text-gray-600">Countries Reached</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-6">
              <Zap className="w-8 h-8 text-purple-600 mx-auto mb-3" />
              <div className="text-2xl font-bold text-gray-900">100,000+</div>
              <div className="text-gray-600">Hours Learned</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-6">
              <DollarSign className="w-8 h-8 text-orange-600 mx-auto mb-3" />
              <div className="text-2xl font-bold text-gray-900">$0</div>
              <div className="text-gray-600">Cost to Users</div>
            </CardContent>
          </Card>
        </div>

        {/* Donation Tiers */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">Choose Your Support Level</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {DONATION_TIERS.map((tier) => {
              const IconComponent = tier.icon;
              const isSelected = selectedAmount === tier.amount && !isCustom;
              
              return (
                <Card 
                  key={tier.amount}
                  className={`relative cursor-pointer transition-all hover:shadow-lg ${
                    isSelected ? 'ring-2 ring-blue-500 shadow-lg' : ''
                  } ${tier.popular ? 'border-blue-500' : ''}`}
                  onClick={() => {
                    setSelectedAmount(tier.amount);
                    setIsCustom(false);
                  }}
                >
                  {tier.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-blue-500 hover:bg-blue-600">Most Popular</Badge>
                    </div>
                  )}
                  <CardHeader className="text-center pb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <IconComponent className="w-6 h-6 text-blue-600" />
                    </div>
                    <CardTitle className="text-lg">{tier.title}</CardTitle>
                    <div className="text-3xl font-bold text-blue-600">${tier.amount}</div>
                    <p className="text-sm text-gray-600">{tier.description}</p>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <ul className="space-y-2">
                      {tier.benefits.map((benefit, index) => (
                        <li key={index} className="flex items-center gap-2 text-sm">
                          <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                          <span>{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Custom Amount */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-center">Or Choose Your Own Amount</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center gap-4">
              <div className="flex items-center">
                <span className="text-2xl font-bold text-gray-700 mr-2">$</span>
                <input
                  type="number"
                  min="1"
                  placeholder="25"
                  value={customAmount}
                  onChange={(e) => {
                    setCustomAmount(e.target.value);
                    setIsCustom(true);
                  }}
                  className="w-24 px-3 py-2 border border-gray-300 rounded-lg text-center text-xl font-bold focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <Button
                onClick={() => handleDonate(parseInt(customAmount) || 25)}
                disabled={!customAmount || parseInt(customAmount) < 1}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Donate Custom Amount
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Main Donate Button */}
        <div className="text-center mb-12">
          <Button
            size="lg"
            onClick={() => handleDonate(isCustom ? parseInt(customAmount) || 25 : selectedAmount)}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold"
          >
            <Heart className="w-5 h-5 mr-2" />
            Donate ${isCustom ? customAmount || '25' : selectedAmount} Now
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
          <p className="text-sm text-gray-600 mt-3">
            Secure payment powered by PayPal • No account required
          </p>
        </div>

        {/* Why We Need Support */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-yellow-500" />
                Where Your Money Goes
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span>Server & Infrastructure</span>
                <span className="font-semibold">40%</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Development & Features</span>
                <span className="font-semibold">35%</span>
              </div>
              <div className="flex justify-between items-center">
                <span>AI & API Services</span>
                <span className="font-semibold">15%</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Support & Maintenance</span>
                <span className="font-semibold">10%</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="w-5 h-5 text-red-500" />
                Our Promise to You
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                <div>
                  <div className="font-medium">Always Free</div>
                  <div className="text-sm text-gray-600">Core features will always be free for everyone</div>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                <div>
                  <div className="font-medium">No Ads</div>
                  <div className="text-sm text-gray-600">Clean, distraction-free learning experience</div>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                <div>
                  <div className="font-medium">Open Source</div>
                  <div className="text-sm text-gray-600">Transparent development and community-driven</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Alternative Support */}
        <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
          <CardHeader>
            <CardTitle className="text-center">Other Ways to Support</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div>
                <Users className="w-8 h-8 text-purple-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Spread the Word</h4>
                <p className="text-sm text-gray-600">Share Learniify with friends and on social media</p>
              </div>
              <div>
                <Star className="w-8 h-8 text-yellow-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Leave a Review</h4>
                <p className="text-sm text-gray-600">Rate us on Google or write a testimonial</p>
              </div>
              <div>
                <Gift className="w-8 h-8 text-green-600 mx-auto mb-3" />
                <h4 className="font-semibold mb-2">Contribute Content</h4>
                <p className="text-sm text-gray-600">Suggest learning resources or create study guides</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Thank You Message */}
        <div className="text-center mt-12 p-8 bg-white rounded-lg shadow-sm">
          <Heart className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Thank You for Supporting Free Education</h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Every donation, no matter the size, makes a real difference in someone's learning journey. 
            Together, we're building a world where quality education is accessible to everyone, everywhere.
          </p>
        </div>
      </div>
    </div>
  );
}