import { useState, useEffect } from "react";
import { usePara<PERSON>, useLocation } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  UserPlus,
  Clock,
  CheckCircle,
  XCircle,
  Mail,
  Sparkles,
  ArrowRight,
  Heart,
} from "lucide-react";

interface FriendInvite {
  id: number;
  fromUserId: string;
  toEmail: string;
  status: string;
  expiresAt: string;
  fromUser: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    profileImageUrl?: string;
  };
}

export default function InvitePage() {
  const { inviteToken } = useParams();
  const [, setLocation] = useLocation();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { toast } = useToast();
  const [isAccepting, setIsAccepting] = useState(false);

  // Fetch invite details
  const {
    data: invite,
    isLoading,
    error,
  } = useQuery({
    queryKey: [`/api/friends/invite/${inviteToken}`],
    enabled: !!inviteToken,
    retry: false,
  });

  // Accept invite mutation
  const acceptInviteMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", "/api/friends/invite/accept", {
        inviteToken,
      });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Welcome to the network! 🎉",
        description: "You're now connected with your friend on Learnify",
      });
      setLocation("/dashboard");
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: "Failed to accept invite. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleAcceptInvite = () => {
    if (!isAuthenticated) {
      // Store invite token and redirect to signup
      localStorage.setItem("pendingInviteToken", inviteToken || "");
      setLocation("/signup");
      return;
    }

    setIsAccepting(true);
    acceptInviteMutation.mutate();
  };

  const handleSignUp = () => {
    localStorage.setItem("pendingInviteToken", inviteToken || "");
    setLocation("/signup");
  };

  const handleLogin = () => {
    localStorage.setItem("pendingInviteToken", inviteToken || "");
    setLocation("/login");
  };

  if (authLoading || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading invitation...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !invite) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              Invalid Invitation
            </h2>
            <p className="text-gray-600 mb-6">
              This invitation link is invalid, expired, or has already been
              used.
            </p>
            <Button onClick={() => setLocation("/")} className="w-full">
              Go to Learnify
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isExpired = new Date(invite.expiresAt) < new Date();
  const isUsed = invite.status !== "pending";

  if (isExpired || isUsed) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <Clock className="w-16 h-16 text-orange-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              {isExpired ? "Invitation Expired" : "Invitation Already Used"}
            </h2>
            <p className="text-gray-600 mb-6">
              {isExpired
                ? "This invitation has expired. Please ask your friend to send a new one."
                : "This invitation has already been accepted."}
            </p>
            <Button onClick={() => setLocation("/")} className="w-full">
              Go to Learnify
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center p-4">
      <div className="w-full max-w-lg">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full mb-4">
            <Sparkles className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Learnify</h1>
          <p className="text-gray-600">Transform Your Learning Journey</p>
        </div>

        {/* Invitation Card */}
        <Card className="overflow-hidden shadow-xl">
          <CardHeader className="bg-gradient-to-r from-purple-600 to-pink-600 text-white text-center pb-8">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <Avatar className="w-20 h-20 border-4 border-white shadow-lg">
                  <AvatarImage src={invite.fromUser.profileImageUrl} />
                  <AvatarFallback className="bg-white text-purple-600 text-xl font-bold">
                    {invite.fromUser.firstName?.[0] ||
                      invite.fromUser.email[0].toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -bottom-2 -right-2 bg-white rounded-full p-1">
                  <Heart className="w-4 h-4 text-red-500" />
                </div>
              </div>
            </div>
            <CardTitle className="text-2xl font-bold mb-2">
              You're Invited! 🎉
            </CardTitle>
            <p className="text-purple-100">
              <strong>
                {invite.fromUser.firstName || invite.fromUser.email}
              </strong>{" "}
              wants you to join them on Learnify
            </p>
          </CardHeader>

          <CardContent className="p-8">
            {/* Features */}
            <div className="space-y-4 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 text-center mb-6">
                What awaits you on Learnify:
              </h3>

              <div className="grid gap-4">
                <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                  <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-sm">📚</span>
                  </div>
                  <span className="text-gray-700">
                    Create personalized learning plans
                  </span>
                </div>

                <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                  <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-sm">📊</span>
                  </div>
                  <span className="text-gray-700">
                    Track progress with detailed analytics
                  </span>
                </div>

                <div className="flex items-center gap-3 p-3 bg-pink-50 rounded-lg">
                  <div className="w-8 h-8 bg-pink-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-sm">👥</span>
                  </div>
                  <span className="text-gray-700">
                    Connect with friends and learn together
                  </span>
                </div>

                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-sm">🤖</span>
                  </div>
                  <span className="text-gray-700">
                    AI-powered content recommendations
                  </span>
                </div>
              </div>
            </div>

            {/* Expiry Notice */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <div className="flex items-center gap-2 text-yellow-800">
                <Clock className="w-4 h-4" />
                <span className="text-sm font-medium">
                  This invitation expires on{" "}
                  {new Date(invite.expiresAt).toLocaleDateString()}
                </span>
              </div>
            </div>

            {/* Action Buttons */}
            {isAuthenticated ? (
              <div className="space-y-3">
                <Button
                  onClick={handleAcceptInvite}
                  disabled={isAccepting || acceptInviteMutation.isPending}
                  className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-3 text-lg font-semibold"
                >
                  {isAccepting || acceptInviteMutation.isPending ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Accepting...
                    </>
                  ) : (
                    <>
                      <UserPlus className="w-5 h-5 mr-2" />
                      Accept Invitation
                    </>
                  )}
                </Button>

                <p className="text-center text-sm text-gray-500">
                  You'll be connected with{" "}
                  {invite.fromUser.firstName || invite.fromUser.email} as
                  friends
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                <Button
                  onClick={handleSignUp}
                  className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-3 text-lg font-semibold"
                >
                  <UserPlus className="w-5 h-5 mr-2" />
                  Join Learnify
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>

                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-2">
                    Already have an account?
                  </p>
                  <Button
                    onClick={handleLogin}
                    variant="outline"
                    className="w-full"
                  >
                    Sign In Instead
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            © 2025 Learnify. Transform your learning journey.
          </p>
        </div>
      </div>
    </div>
  );
}
