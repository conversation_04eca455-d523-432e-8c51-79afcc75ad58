import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "wouter";
import { ArrowLeft, Shield, Lock, Eye, Server, AlertTriangle, CheckCircle } from "lucide-react";

export default function Security() {
  return (
    <div className="min-h-screen bg-slate-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="outline" size="sm" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
          <div className="flex items-center gap-3 mb-4">
            <Shield className="w-8 h-8 text-green-500" />
            <h1 className="text-3xl font-bold text-gray-900">Security & Data Protection</h1>
          </div>
          <p className="text-gray-600">
            Learn how we protect your data and maintain the security of our platform.
          </p>
        </div>

        {/* Security Overview */}
        <Card className="mb-6 bg-green-50 border-green-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-800">
              <CheckCircle className="w-5 h-5" />
              Our Security Commitment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-green-700">
              At Learnify, we take security seriously. We implement industry-standard security measures 
              to protect your personal information, learning data, and ensure a safe learning environment 
              for all our users.
            </p>
          </CardContent>
        </Card>

        {/* Data Encryption */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="w-5 h-5 text-blue-500" />
              Data Encryption & Transmission
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Encryption in Transit</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>All data transmitted between your device and our servers is encrypted using TLS 1.3</li>
                <li>HTTPS encryption protects your login credentials and personal information</li>
                <li>API communications are secured with industry-standard protocols</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Encryption at Rest</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>Your personal data is encrypted when stored in our databases</li>
                <li>Learning progress and video history are protected with AES-256 encryption</li>
                <li>Backup data is encrypted and stored in secure, geographically distributed locations</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Authentication & Access Control */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5 text-purple-500" />
              Authentication & Access Control
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">User Authentication</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>Secure OAuth 2.0 integration with Google for streamlined login</li>
                <li>Password-based authentication with bcrypt hashing (minimum 12 rounds)</li>
                <li>Session management with secure, httpOnly cookies</li>
                <li>Automatic session expiration and activity-based logout</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Access Controls</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>Role-based access control (RBAC) for different user permissions</li>
                <li>Principle of least privilege - users only access their own data</li>
                <li>Multi-factor authentication support for enhanced security</li>
                <li>Regular access reviews and permission audits</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Infrastructure Security */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="w-5 h-5 text-indigo-500" />
              Infrastructure & Platform Security
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Cloud Infrastructure</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>Hosted on AWS with SOC 2 Type II compliance</li>
                <li>Auto-scaling infrastructure with load balancing</li>
                <li>Regular security patches and system updates</li>
                <li>Network isolation and firewall protection</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Database Security</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>Supabase PostgreSQL with row-level security (RLS)</li>
                <li>Database connection encryption and access logging</li>
                <li>Regular automated backups with point-in-time recovery</li>
                <li>Database activity monitoring and anomaly detection</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Privacy & Data Protection */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-green-500" />
              Privacy & Data Protection
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Data Minimization</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>We only collect data necessary for providing our services</li>
                <li>Personal information is anonymized where possible</li>
                <li>Automatic data retention policies and secure deletion</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Compliance</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>GDPR compliance for European users</li>
                <li>CCPA compliance for California residents</li>
                <li>Regular privacy impact assessments</li>
                <li>Data processing agreements with third-party services</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Security Monitoring */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-orange-500" />
              Security Monitoring & Incident Response
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Continuous Monitoring</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>24/7 security monitoring and alerting systems</li>
                <li>Automated threat detection and response</li>
                <li>Regular vulnerability scans and penetration testing</li>
                <li>Security event logging and analysis</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Incident Response</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>Dedicated security incident response team</li>
                <li>Established procedures for breach notification</li>
                <li>Regular security drills and response testing</li>
                <li>Transparent communication during security events</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Third-Party Security */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Third-Party Service Security</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              We carefully vet all third-party services and ensure they meet our security standards:
            </p>
            <div className="space-y-3">
              <div>
                <h4 className="font-medium text-gray-900">Google Services</h4>
                <p className="text-gray-600 text-sm">
                  OAuth authentication and YouTube integration - ISO 27001 certified, SOC 2 compliant
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Supabase</h4>
                <p className="text-gray-600 text-sm">
                  Database and authentication services - SOC 2 Type II compliant, GDPR compliant
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">AWS</h4>
                <p className="text-gray-600 text-sm">
                  Cloud infrastructure - Multiple compliance certifications including SOC, ISO, PCI DSS
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* User Security Best Practices */}
        <Card className="mb-6 bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-blue-800">Security Best Practices for Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-blue-700">
              <div>
                <h4 className="font-medium">Protect Your Account</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Use a strong, unique password for your Learnify account</li>
                  <li>Enable two-factor authentication when available</li>
                  <li>Log out from shared or public devices</li>
                  <li>Keep your browser and devices updated</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium">Report Security Issues</h4>
                <p className="text-sm">
                  If you notice any suspicious activity or potential security issues, 
                  please contact us <NAME_EMAIL>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact & Updates */}
        <Card className="bg-gray-50 border-gray-200">
          <CardContent className="p-6">
            <h3 className="font-semibold text-gray-900 mb-2">Security Updates & Contact</h3>
            <p className="text-gray-600 mb-4">
              We regularly update our security measures and will notify users of any significant changes. 
              For security-related questions or to report vulnerabilities, please contact our security team.
            </p>
            <div className="flex gap-3">
              <Link href="/contact">
                <Button variant="outline">
                  Contact Security Team
                </Button>
              </Link>
              <Link href="/privacy-policy">
                <Button variant="outline">
                  Privacy Policy
                </Button>
              </Link>
            </div>
            <p className="text-sm text-gray-500 mt-4">
              Last updated: {new Date().toLocaleDateString()} | 
              Security policy version 2.1
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}