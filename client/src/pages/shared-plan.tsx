import { useParams } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { BookOpen, Play, Clock, User, CheckCircle } from "lucide-react";

interface SharedPlanData {
  plan: {
    id: number;
    title: string;
    description: string;
    createdAt: string;
  };
  videos: Array<{
    id: number;
    orderIndex: number;
    video: {
      id: number;
      youtubeId: string;
      title: string;
      description: string;
      thumbnailUrl: string;
      duration: string;
      channelTitle: string;
    };
  }>;
}

export default function SharedPlan() {
  const { shareToken } = useParams();
  const { toast } = useToast();
  const { isAuthenticated } = useAuth();

  const { data: sharedPlan, isLoading } = useQuery({
    queryKey: ['/api/shared-plan', shareToken],
    queryFn: async () => {
      const response = await fetch(`/api/shared-plan/${shareToken}`);
      if (!response.ok) {
        throw new Error('Plan not found');
      }
      return response.json() as Promise<SharedPlanData>;
    },
    enabled: !!shareToken,
  });

  const acceptPlanMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", `/api/shared-plan/${shareToken}/accept`);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Success!",
        description: "Learning plan added to your dashboard",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/learning-plans'] });
    },
    onError: (error) => {
      console.error("Error accepting plan:", error);
      toast({
        title: "Error",
        description: "Failed to add plan to your dashboard",
        variant: "destructive",
      });
    },
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-slate-600">Loading shared plan...</p>
        </div>
      </div>
    );
  }

  if (!sharedPlan) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-slate-800 mb-2">Plan Not Found</h1>
          <p className="text-slate-600">The shared learning plan could not be found.</p>
        </div>
      </div>
    );
  }

  const { plan, videos } = sharedPlan;

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-slate-200 mb-6">
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-2xl font-bold text-slate-800 mb-2">{plan.title}</h1>
              <p className="text-slate-600 mb-4">{plan.description}</p>
              <div className="flex items-center space-x-4 text-sm text-slate-500">
                <div className="flex items-center space-x-1">
                  <BookOpen className="w-4 h-4" />
                  <span>{videos.length} videos</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>~{Math.ceil(videos.length * 0.5)} hours</span>
                </div>
                <div className="flex items-center space-x-1">
                  <User className="w-4 h-4" />
                  <span>Shared Plan</span>
                </div>
              </div>
            </div>
            {isAuthenticated && (
              <Button
                onClick={() => acceptPlanMutation.mutate()}
                disabled={acceptPlanMutation.isPending}
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                {acceptPlanMutation.isPending ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Adding...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4" />
                    <span>Add to My Plans</span>
                  </div>
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Video List */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-slate-800 mb-4">Course Content</h2>
          {videos.sort((a, b) => a.orderIndex - b.orderIndex).map((planVideo, index) => {
            const video = planVideo.video;
            return (
              <Card key={video.id} className="bg-white shadow-sm border border-slate-200">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="relative">
                        <img
                          src={video.thumbnailUrl}
                          alt={video.title}
                          className="w-32 h-20 rounded-md object-cover"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all duration-200 rounded-md flex items-center justify-center">
                          <Play className="w-8 h-8 text-white opacity-0 hover:opacity-100 transition-opacity" />
                        </div>
                      </div>
                    </div>
                    <div className="flex-grow">
                      <div className="flex items-start justify-between">
                        <div>
                          <Badge variant="outline" className="mb-2">
                            {index + 1}
                          </Badge>
                          <h3 className="font-medium text-slate-800 mb-1 line-clamp-2">
                            {video.title}
                          </h3>
                          <p className="text-sm text-slate-600 mb-2 line-clamp-2">
                            {video.description}
                          </p>
                          <div className="flex items-center space-x-3 text-xs text-slate-500">
                            <span>{video.channelTitle}</span>
                            <span>•</span>
                            <span>{video.duration}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {!isAuthenticated && (
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">Want to add this plan?</h3>
            <p className="text-blue-600 mb-4">Sign in to add this learning plan to your dashboard and track your progress.</p>
            <Button
              onClick={() => window.location.href = '/api/login'}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              Sign In
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}