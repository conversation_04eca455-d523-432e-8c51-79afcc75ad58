import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, FileText, AlertTriangle, Youtube, Scale } from "lucide-react";
import { <PERSON> } from "wouter";

export default function TermsOfService() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <Link href="/">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Terms of Service</h1>
          <p className="text-gray-600">Last updated: {new Date().toLocaleDateString()}</p>
        </div>

        <div className="space-y-6">
          {/* Introduction */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Agreement to Terms
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                Welcome to Learnify. These Terms of Service ("Terms") govern your use of our website 
                and services. By accessing or using Learnify, you agree to be bound by these Terms.
              </p>
              <p>
                If you do not agree to these Terms, please do not use our services.
              </p>
            </CardContent>
          </Card>

          {/* Service Description */}
          <Card>
            <CardHeader>
              <CardTitle>Description of Service</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                Learnify is an educational platform that helps users discover, organize, and track 
                their learning progress through curated educational content, primarily sourced from 
                YouTube and other educational platforms.
              </p>
              <div>
                <h3 className="font-semibold mb-2">Our Services Include:</h3>
                <ul className="list-disc pl-6 space-y-1">
                  <li>Educational content discovery and search</li>
                  <li>Learning plan creation and management</li>
                  <li>Progress tracking and analytics</li>
                  <li>Personalized recommendations</li>
                  <li>Social learning features</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* YouTube API Terms */}
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="flex items-center text-red-800">
                <Youtube className="w-5 h-5 mr-2" />
                YouTube API Services
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 text-red-700">
              <p>
                <strong>IMPORTANT:</strong> Our service uses YouTube API Services. By using Learnify, 
                you agree to be bound by the YouTube Terms of Service.
              </p>
              <div className="space-y-2">
                <p>
                  <strong>YouTube Terms of Service:</strong> 
                  <a 
                    href="https://www.youtube.com/t/terms" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-red-600 hover:underline ml-1"
                  >
                    https://www.youtube.com/t/terms
                  </a>
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">YouTube Content Usage:</h3>
                <ul className="list-disc pl-6 space-y-1">
                  <li>We display YouTube content through embedded players only</li>
                  <li>We do not download, store, or redistribute YouTube videos</li>
                  <li>All YouTube content remains subject to YouTube's terms and policies</li>
                  <li>Content creators retain all rights to their YouTube content</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* User Accounts */}
          <Card>
            <CardHeader>
              <CardTitle>User Accounts and Registration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Account Creation:</h3>
                <ul className="list-disc pl-6 space-y-1">
                  <li>You must provide accurate and complete information</li>
                  <li>You are responsible for maintaining account security</li>
                  <li>You must be at least 13 years old to create an account</li>
                  <li>One person may not maintain multiple accounts</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Account Responsibilities:</h3>
                <ul className="list-disc pl-6 space-y-1">
                  <li>Keep your login credentials secure</li>
                  <li>Notify us immediately of any unauthorized access</li>
                  <li>You are responsible for all activities under your account</li>
                  <li>Do not share your account with others</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Acceptable Use */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Scale className="w-5 h-5 mr-2" />
                Acceptable Use Policy
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">You May:</h3>
                <ul className="list-disc pl-6 space-y-1">
                  <li>Use our service for personal, educational purposes</li>
                  <li>Create and share learning plans</li>
                  <li>Interact respectfully with other users</li>
                  <li>Provide feedback to improve our services</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-2 text-red-600">You May Not:</h3>
                <ul className="list-disc pl-6 space-y-1 text-red-600">
                  <li>Use our service for any illegal or unauthorized purpose</li>
                  <li>Attempt to gain unauthorized access to our systems</li>
                  <li>Upload malicious code or attempt to disrupt our service</li>
                  <li>Harass, abuse, or harm other users</li>
                  <li>Violate any applicable laws or regulations</li>
                  <li>Scrape or automatically extract data from our service</li>
                  <li>Impersonate others or provide false information</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Intellectual Property */}
          <Card>
            <CardHeader>
              <CardTitle>Intellectual Property Rights</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Our Content:</h3>
                <p>
                  The Learnify platform, including its design, features, and original content, 
                  is owned by us and protected by intellectual property laws.
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Third-Party Content:</h3>
                <p>
                  Educational content accessed through our platform (including YouTube videos) 
                  remains the property of its respective owners. We do not claim ownership of 
                  third-party content.
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">User-Generated Content:</h3>
                <p>
                  You retain ownership of content you create (learning plans, notes, etc.), 
                  but grant us a license to use it to provide our services.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Privacy and Data */}
          <Card>
            <CardHeader>
              <CardTitle>Privacy and Data Protection</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                Your privacy is important to us. Please review our Privacy Policy to understand 
                how we collect, use, and protect your information.
              </p>
              <p>
                <Link href="/privacy-policy">
                  <Button variant="outline" size="sm">
                    View Privacy Policy
                  </Button>
                </Link>
              </p>
            </CardContent>
          </Card>

          {/* Service Availability */}
          <Card>
            <CardHeader>
              <CardTitle>Service Availability and Modifications</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                We strive to provide reliable service but cannot guarantee 100% uptime. 
                We reserve the right to:
              </p>
              <ul className="list-disc pl-6 space-y-1">
                <li>Modify or discontinue features with reasonable notice</li>
                <li>Perform maintenance that may temporarily affect service</li>
                <li>Update these Terms as needed</li>
                <li>Suspend or terminate accounts that violate our Terms</li>
              </ul>
            </CardContent>
          </Card>

          {/* Disclaimers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2" />
                Disclaimers and Limitations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Educational Content:</h3>
                <p>
                  We curate educational content but do not guarantee its accuracy, completeness, 
                  or suitability for your specific needs. Always verify information from multiple sources.
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Third-Party Services:</h3>
                <p>
                  Our platform integrates with third-party services (like YouTube). We are not 
                  responsible for the availability, content, or policies of these services.
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Limitation of Liability:</h3>
                <p>
                  To the maximum extent permitted by law, we shall not be liable for any indirect, 
                  incidental, special, or consequential damages arising from your use of our service.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Termination */}
          <Card>
            <CardHeader>
              <CardTitle>Account Termination</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">By You:</h3>
                <p>
                  You may terminate your account at any time by contacting us or using 
                  account deletion features in your settings.
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">By Us:</h3>
                <p>
                  We may suspend or terminate your account if you violate these Terms, 
                  engage in harmful behavior, or for other legitimate reasons.
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Effect of Termination:</h3>
                <p>
                  Upon termination, your access to the service will cease, and we may 
                  delete your account data in accordance with our Privacy Policy.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Governing Law */}
          <Card>
            <CardHeader>
              <CardTitle>Governing Law and Disputes</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                These Terms are governed by the laws of India. Any disputes 
                will be resolved through binding arbitration or in the courts of Hyderabad, India.
              </p>
              <p>
                We encourage resolving disputes through direct communication before 
                pursuing legal action.
              </p>
            </CardContent>
          </Card>

          {/* Changes to Terms */}
          <Card>
            <CardHeader>
              <CardTitle>Changes to These Terms</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                We may update these Terms from time to time. We will notify users of 
                significant changes via email or platform notifications. Continued use 
                of our service after changes constitutes acceptance of the new Terms.
              </p>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>If you have questions about these Terms, please contact us:</p>
              <div className="space-y-2">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Support:</strong> <EMAIL></p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}