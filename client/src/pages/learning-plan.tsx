import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { apiRequest } from "@/lib/queryClient";
import VideoCard from "@/components/video-card";
import { calculateTotalDuration } from "@/lib/duration";
import AddVideoModal from "@/components/add-video-modal";
import SharePlanModal from "@/components/share-plan-modal";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowLeft,
  Play,
  MoreVertical,
  Plus,
  Share2,
  <PERSON><PERSON>,
  Heart,
  Edit2,
  Check,
  X,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface LearningPlan {
  id: number;
  title: string;
  description: string;
  slug: string;
  isActive: boolean;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  permissions?: {
    canView: boolean;
    canAddVideos: boolean;
    canShare: boolean;
    isOwner: boolean;
  };
}

interface PlanVideo {
  id: number;
  planId: number;
  videoId: number;
  orderIndex: number;
  video: {
    id: number;
    youtubeId: string;
    title: string;
    description: string;
    thumbnailUrl: string;
    duration: string;
    channelTitle: string;
  };
}

interface VideoProgress {
  id: number;
  userId: string;
  videoId: number;
  currentTime: number;
  isCompleted: boolean;
  lastWatched: string;
}

// Video Heart Button Component
function VideoHeartButton({ videoId }: { videoId: number }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Check if video is favorited
  const { data: favoriteStatus } = useQuery({
    queryKey: [`/api/favorites/videos/${videoId}/status`],
  });

  const isFavorited = favoriteStatus?.isFavorited || false;

  // Toggle favorite mutation
  const toggleFavoriteMutation = useMutation({
    mutationFn: async () => {
      if (isFavorited) {
        return await apiRequest(`/api/favorites/videos/${videoId}`, {
          method: "DELETE"
        });
      } else {
        return await apiRequest(`/api/favorites/videos/${videoId}`, {
          method: "POST"
        });
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/favorites/videos/${videoId}/status`] });
      queryClient.invalidateQueries({ queryKey: ['/api/favorites/videos'] });
      toast({
        title: isFavorited ? "Removed from Favorites" : "Added to Favorites",
        description: isFavorited 
          ? "Video removed from your favorites" 
          : "Video added to your favorites",
      });
    },
    onError: (error) => {
      console.error("Error toggling favorite:", error);
      toast({
        title: "Error",
        description: "Failed to update favorites",
        variant: "destructive",
      });
    },
  });

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    toggleFavoriteMutation.mutate();
  };

  return (
    <Button
      onClick={handleToggleFavorite}
      disabled={toggleFavoriteMutation.isPending}
      size="sm"
      variant="outline"
      className={`${isFavorited ? 'text-red-600 border-red-600 hover:bg-red-50' : 'hover:text-red-500'}`}
      title={isFavorited ? 'Remove from favorites' : 'Add to favorites'}
    >
      <Heart className={`w-3 h-3 ${isFavorited ? 'fill-current' : ''}`} />
    </Button>
  );
}

export default function LearningPlan() {
  const { slug } = useParams();
  const { toast } = useToast();
  const { user, isAuthenticated, isLoading } = useAuth();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState("");
  const [editedDescription, setEditedDescription] = useState("");

  // Scroll to top when component mounts (mobile fix)
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'instant' });
  }, [slug]); // Re-run when slug changes

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, toast]);

  const { data: plan, isLoading: planLoading } = useQuery<LearningPlan>({
    queryKey: [`/api/learning-plans/slug/${slug}`],
    enabled: isAuthenticated && !!slug,
  });

  // Update edited title and description when plan loads
  useEffect(() => {
    if (plan?.title) {
      setEditedTitle(plan.title);
    }
    if (plan?.description) {
      setEditedDescription(plan.description);
    }
  }, [plan?.title, plan?.description]);

  const { data: planVideos = [], isLoading: videosLoading } = useQuery<
    PlanVideo[]
  >({
    queryKey: [`/api/learning-plans/${plan?.id}/videos`],
    enabled: isAuthenticated && !!plan?.id,
  });

  const { data: progress = [] } = useQuery<VideoProgress[]>({
    queryKey: [`/api/progress/plan/${plan?.id}`],
    enabled: isAuthenticated && !!plan?.id,
    staleTime: 0,
    cacheTime: 0,
  });

  // Check if playlist is favorited
  const { data: favoriteStatus } = useQuery({
    queryKey: [`/api/favorites/playlists/${plan?.id}/status`],
    enabled: isAuthenticated && !!plan?.id,
  });

  const isFavorited = favoriteStatus?.isFavorited || false;

  // Toggle favorite mutation
  const toggleFavoriteMutation = useMutation({
    mutationFn: async () => {
      if (isFavorited) {
        return await apiRequest(`/api/favorites/playlists/${plan?.id}`, {
          method: "DELETE"
        });
      } else {
        return await apiRequest(`/api/favorites/playlists/${plan?.id}`, {
          method: "POST"
        });
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/favorites/playlists/${plan?.id}/status`] });
      queryClient.invalidateQueries({ queryKey: ['/api/favorites/playlists'] });
      toast({
        title: isFavorited ? "Removed from Favorites" : "Added to Favorites",
        description: isFavorited 
          ? "Playlist removed from your favorites" 
          : "Playlist added to your favorites",
      });
    },
    onError: (error) => {
      console.error("Error toggling favorite:", error);
      toast({
        title: "Error",
        description: "Failed to update favorites",
        variant: "destructive",
      });
    },
  });

  const handleToggleFavorite = () => {
    toggleFavoriteMutation.mutate();
  };

  // Update plan title mutation
  const updatePlanMutation = useMutation({
    mutationFn: async (updates: { title?: string; description?: string }) => {
      const response = await apiRequest(`/api/learning-plans/${plan?.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/learning-plans/slug/${slug}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/learning-plans'] });
      setIsEditing(false);
      toast({
        title: "Success",
        description: "Plan updated successfully",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to update plan",
        variant: "destructive",
      });
    },
  });

  const handleSave = () => {
    const updates: { title?: string; description?: string } = {};
    if (editedTitle.trim() !== plan?.title) {
      updates.title = editedTitle.trim();
    }
    if (editedDescription.trim() !== plan?.description) {
      updates.description = editedDescription.trim();
    }
    
    if (Object.keys(updates).length > 0) {
      updatePlanMutation.mutate(updates);
    } else {
      setIsEditing(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedTitle(plan?.title || "");
    setEditedDescription(plan?.description || "");
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  const removeVideoMutation = useMutation({
    mutationFn: async (videoId: number) => {
      const response = await fetch(`/api/learning-plans/${plan?.id}/videos/${videoId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(errorData || 'Failed to remove video');
      }
      
      return response;
    },
    onMutate: async (videoId: number) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: [`/api/learning-plans/${plan?.id}/videos`] });
      
      // Snapshot the previous value
      const previousVideos = queryClient.getQueryData([`/api/learning-plans/${plan?.id}/videos`]);
      
      // Optimistically update to remove the video
      queryClient.setQueryData([`/api/learning-plans/${plan?.id}/videos`], (old: any) => {
        return old?.filter((pv: any) => pv.video.id !== videoId) || [];
      });
      
      return { previousVideos };
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Video removed from learning plan",
      });
    },
    onError: (error, videoId, context) => {
      // Rollback on error
      if (context?.previousVideos) {
        queryClient.setQueryData([`/api/learning-plans/${plan?.id}/videos`], context.previousVideos);
      }
      
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to remove video",
        variant: "destructive",
      });
    },
  });

  const shareplanMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest(
        "POST",
        `/api/learning-plans/${plan?.id}/share`
      );
      return response.json();
    },
    onSuccess: (data) => {
      navigator.clipboard.writeText(data.shareUrl);
      toast({
        title: "Link Copied!",
        description: "Share link has been copied to your clipboard",
      });
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to generate share link",
        variant: "destructive",
      });
    },
  });

  if (isLoading || planLoading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-600">Loading learning plan...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !plan) {
    return null;
  }

  const completedVideos = progress.filter(
    (p: VideoProgress) => p.isCompleted
  ).length;
  const totalVideos = planVideos.length;
  const progressPercentage =
    totalVideos > 0 ? (completedVideos / totalVideos) * 100 : 0;
  
  // Debug logging
  console.log('📊 Progress calculation:', {
    planId: plan.id,
    totalVideos,
    completedVideos,
    progressPercentage,
    progressData: progress.map(p => ({ videoId: p.videoId, isCompleted: p.isCompleted }))
  });
  
  // Debug progress calculation
  console.log('📊 Plan progress debug:', {
    planId: plan?.id,
    totalVideos,
    completedVideos,
    progressPercentage,
    progressData: progress.map(p => ({ videoId: p.videoId, isCompleted: p.isCompleted }))
  });
  
  // Debug user info
  console.log('👤 User info:', {
    user,
    isAuthenticated
  });

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Link href="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
        </div>

        {/* Plan Overview */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  {isEditing ? (
                    <div className="flex items-center gap-2 flex-1">
                      <input
                        type="text"
                        value={editedTitle}
                        onChange={(e) => setEditedTitle(e.target.value)}
                        onKeyDown={handleKeyPress}
                        className="text-2xl font-semibold bg-transparent border-b-2 border-blue-500 focus:outline-none flex-1 min-w-0"
                        autoFocus
                      />
                      <Button
                        onClick={handleSave}
                        disabled={updatePlanMutation.isPending}
                        size="sm"
                        variant="ghost"
                        className="text-green-600 hover:text-green-700 hover:bg-green-50"
                      >
                        <Check className="w-4 h-4" />
                      </Button>
                      <Button
                        onClick={handleCancel}
                        disabled={updatePlanMutation.isPending}
                        size="sm"
                        variant="ghost"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ) : (
                    <>
                      <CardTitle className="text-2xl">{plan.title}</CardTitle>
                      {plan.permissions?.isOwner && (
                        <Button
                          onClick={() => setIsEditing(true)}
                          size="sm"
                          variant="ghost"
                          className="text-slate-500 hover:text-slate-700 hover:bg-slate-100"
                          title="Edit plan"
                        >
                          <Edit2 className="w-4 h-4" />
                        </Button>
                      )}
                    </>
                  )}
                </div>
                {isEditing ? (
                  <Textarea
                    value={editedDescription}
                    onChange={(e) => setEditedDescription(e.target.value)}
                    onKeyDown={handleKeyPress}
                    className="mb-4 min-h-[80px] border-2 border-blue-500 focus:border-blue-600"
                    placeholder="Enter plan description..."
                  />
                ) : (
                  <p className="text-slate-600 mb-4">{plan.description}</p>
                )}
                <div className="flex items-center space-x-6 text-sm text-slate-500">
                  <span>{totalVideos} videos</span>
                  <span>{completedVideos} completed</span>
                  <span>{calculateTotalDuration(planVideos.map(pv => pv.video))} total</span>
                </div>
              </div>
              <div className="flex items-center space-x-4 flex-shrink-0">
                <Button
                  onClick={handleToggleFavorite}
                  disabled={toggleFavoriteMutation.isPending}
                  variant="outline"
                  size="sm"
                  className={isFavorited ? 'text-red-600 border-red-600 hover:bg-red-50' : ''}
                >
                  <Heart className={`w-4 h-4 mr-2 ${isFavorited ? 'fill-current' : ''}`} />
                  {isFavorited ? 'Favorited' : 'Favorite'}
                </Button>
                <SharePlanModal planId={plan.id} planTitle={plan.title}>
                  <Button variant="outline" size="sm">
                    <Share2 className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                </SharePlanModal>
                <div className="text-right">
                  <div className="text-2xl font-bold text-slate-800 mb-1">
                    {Math.round(progressPercentage)}%
                  </div>
                  <div className="text-sm text-slate-500">Complete</div>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Progress value={progressPercentage} className="mb-4" />
            {totalVideos > 0 && (
              <div className="flex justify-between items-center">
                <Button
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                  asChild
                >
                  <Link href={`/video/${planVideos[0]?.video.youtubeId}?plan=${plan.slug}`}>
                    <Play className="w-4 h-4 mr-2" />
                    Continue Learning
                  </Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Videos List */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold text-slate-800">Videos</h3>
            {plan.permissions?.canAddVideos && (
              <AddVideoModal planId={plan.id}>
                <Button
                  size="sm"
                  variant="outline"
                  className="text-blue-500 border-blue-500 hover:bg-blue-50"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Video
                </Button>
              </AddVideoModal>
            )}
          </div>

          {videosLoading ? (
            <div className="grid gap-4">
              {[...Array(3)].map((_, i) => (
                <div
                  key={i}
                  className="bg-white rounded-xl border border-slate-200 p-4 animate-pulse"
                >
                  <div className="flex space-x-4">
                    <div className="w-32 h-20 bg-slate-200 rounded-lg"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                      <div className="h-3 bg-slate-200 rounded w-1/2"></div>
                      <div className="h-3 bg-slate-200 rounded w-1/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : planVideos.length === 0 ? (
            <Card>
              <CardContent className="py-12 text-center">
                <div className="text-slate-400 mb-4">
                  <Play className="w-12 h-12 mx-auto" />
                </div>
                <h4 className="text-lg font-medium text-slate-800 mb-2">
                  No videos yet
                </h4>
                <p className="text-slate-600 mb-4">
                  Start building your learning plan by adding educational
                  videos.
                </p>
                {plan.permissions?.canAddVideos && (
                  <AddVideoModal planId={plan.id}>
                    <Button className="bg-blue-500 hover:bg-blue-600 text-white">
                      <Plus className="w-4 h-4 mr-2" />
                      Add Video
                    </Button>
                  </AddVideoModal>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {planVideos.map((planVideo: PlanVideo) => {
                const videoProgress = progress.find(
                  (p: VideoProgress) => p.videoId === planVideo.video.id
                );

                return (
                  <Card
                    key={planVideo.id}
                    className="hover:shadow-md transition-shadow"
                  >
                    <CardContent className="p-4">
                      <div className="flex space-x-4">
                        <Link
                          href={`/video/${planVideo.video.youtubeId}?plan=${plan.slug}`}
                          className="flex-shrink-0"
                        >
                          <img
                            src={
                              planVideo.video.thumbnailUrl ||
                              "/api/placeholder/320/180"
                            }
                            alt={planVideo.video.title}
                            className="w-32 h-20 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
                          />
                        </Link>

                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start mb-2">
                            <Link href={`/video/${planVideo.video.youtubeId}?plan=${plan.slug}`}>
                              <h4 className="font-semibold text-slate-800 hover:text-blue-600 transition-colors cursor-pointer line-clamp-2">
                                {planVideo.video.title}
                              </h4>
                            </Link>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreVertical className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent>
                                <DropdownMenuItem
                                  onClick={() => {
                                    if (confirm(
                                      `⚠️ Remove "${planVideo.video.title}" from this plan?\n\nThis action cannot be undone. The video will be permanently removed from your learning plan.`
                                    )) {
                                      removeVideoMutation.mutate(planVideo.video.id);
                                    }
                                  }}
                                  className="text-red-600"
                                >
                                  Remove from plan
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>

                          <p className="text-sm text-slate-600 mb-2 line-clamp-2">
                            {planVideo.video.description}
                          </p>

                          <div className="flex items-center space-x-4 text-xs text-slate-500 mb-3">
                            <span>{planVideo.video.channelTitle}</span>
                            <span>{planVideo.video.duration}</span>
                            {videoProgress && (
                              <span
                                className={
                                  videoProgress.isCompleted
                                    ? "text-green-600"
                                    : "text-blue-600"
                                }
                              >
                                {videoProgress.isCompleted
                                  ? "Completed"
                                  : videoProgress.currentTime > 0
                                  ? "In Progress"
                                  : "Not Started"}
                              </span>
                            )}
                          </div>

                          {videoProgress && !videoProgress.isCompleted && videoProgress.currentTime > 0 && (
                            <div className="w-full bg-slate-200 rounded-full h-2 mb-3">
                              <div
                                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                style={{
                                  width: "30%",
                                }}
                              />
                            </div>
                          )}

                          {/* Action Buttons */}
                          <div className="flex items-center gap-2">
                            <Link href={`/video/${planVideo.video.youtubeId}?plan=${plan.slug}`}>
                              <Button size="sm" className="bg-blue-500 hover:bg-blue-600 text-white">
                                <Play className="w-3 h-3 mr-1" />
                                Watch
                              </Button>
                            </Link>
                            <VideoHeartButton videoId={planVideo.video.id} />
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
