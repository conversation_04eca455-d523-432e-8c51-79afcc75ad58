import { useEffect, useState, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import YouTubePlayer from "@/components/youtube-player";
import PlaylistSidebar from "@/components/playlist-sidebar";
import RelatedVideos from "@/components/related-videos";
import AddVideoModal from "@/components/add-video-modal";
import { ArrowLeft, CheckCircle, ExternalLink, Play, Clock, Plus, Heart } from "lucide-react";

interface Video {
  id: number;
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  duration: string;
  channelTitle: string;
  publishedAt: string;
  viewCount: number;
}

interface VideoProgress {
  id: number;
  userId: string;
  videoId: number;
  currentTime: number;
  isCompleted: boolean;
  lastWatched: string;
}

// Access temp video cache (same reference as recommendations)
const getTempVideo = (tempId: string) => {
  if (typeof window !== 'undefined') {
    if (!window.tempVideoCache) {
      window.tempVideoCache = new Map();
    }
    return window.tempVideoCache.get(tempId);
  }
  return null;
};

// Video Heart Button Component
function VideoHeartButton({ videoId }: { videoId: number }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Check if video is favorited
  const { data: favoriteStatus } = useQuery({
    queryKey: [`/api/favorites/videos/${videoId}/status`],
    enabled: !!videoId,
  });

  const isFavorited = favoriteStatus?.isFavorited || false;

  // Toggle favorite mutation
  const toggleFavoriteMutation = useMutation({
    mutationFn: async () => {
      if (isFavorited) {
        return await apiRequest(`/api/favorites/videos/${videoId}`, {
          method: "DELETE"
        });
      } else {
        return await apiRequest(`/api/favorites/videos/${videoId}`, {
          method: "POST"
        });
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/favorites/videos/${videoId}/status`] });
      queryClient.invalidateQueries({ queryKey: ['/api/favorites/videos'] });
      toast({
        title: isFavorited ? "Removed from Favorites" : "Added to Favorites",
        description: isFavorited 
          ? "Video removed from your favorites" 
          : "Video added to your favorites",
      });
    },
    onError: (error) => {
      console.error("Error toggling favorite:", error);
      toast({
        title: "Error",
        description: "Failed to update favorites",
        variant: "destructive",
      });
    },
  });

  const handleToggleFavorite = () => {
    toggleFavoriteMutation.mutate();
  };

  return (
    <Button
      onClick={handleToggleFavorite}
      disabled={toggleFavoriteMutation.isPending}
      size="sm"
      variant="outline"
      className={`${isFavorited ? 'text-red-600 border-red-600 hover:bg-red-50' : 'hover:text-red-500'}`}
      title={isFavorited ? 'Remove from favorites' : 'Add to favorites'}
    >
      <Heart className={`w-4 h-4 mr-1 ${isFavorited ? 'fill-current' : ''}`} />
      {isFavorited ? 'Favorited' : 'Favorite'}
    </Button>
  );
}

export default function VideoPlayer() {
  const { id } = useParams();
  const [location] = useLocation();
  const { toast } = useToast();
  const { isAuthenticated, isLoading } = useAuth();
  const queryClient = useQueryClient();
  
  // Get plan slug from URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const planSlug = urlParams.get('plan');
  
  console.log('Current location:', window.location.href);
  console.log('Plan slug:', planSlug);
  
  // Handle numeric IDs, YouTube IDs, and temporary IDs
  const isNumericId = /^\d+$/.test(id!);
  const isTempId = id!.startsWith('temp_');
  const videoId = isNumericId ? parseInt(id!) : null;
  const youtubeId = !isNumericId && !isTempId ? id! : null;
  const tempVideo = isTempId ? getTempVideo(id!) : null;
  
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [autoPlayEnabled, setAutoPlayEnabled] = useState(() => {
    // Get from localStorage or default to true
    const saved = localStorage.getItem('autoPlayEnabled');
    return saved !== null ? JSON.parse(saved) : true;
  });
  const [showAutoPlayCountdown, setShowAutoPlayCountdown] = useState(false);
  const [countdown, setCountdown] = useState(5);
  const [isCompleted, setIsCompleted] = useState(false);
  
  // Get plan data for playlist
  const { data: planData } = useQuery({
    queryKey: planSlug ? [`/api/learning-plans/slug/${planSlug}`] : [],
    enabled: !!planSlug,
  });
  
  const { data: userPlans = [] } = useQuery({
    queryKey: ['/api/learning-plans'],
    enabled: isAuthenticated,
    retry: false,
  });

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      toast({
        title: "Unauthorized",
        description: "You are logged out. Logging in again...",
        variant: "destructive",
      });
      setTimeout(() => {
        window.location.href = "/api/login";
      }, 500);
      return;
    }
  }, [isAuthenticated, isLoading, toast]);

  const { data: video, isLoading: videoLoading, error: videoError } = useQuery({
    queryKey: youtubeId ? [`/api/videos/youtube/${youtubeId}`] : [`/api/videos/${videoId}`],
    enabled: isAuthenticated && (!!videoId || !!youtubeId) && !tempVideo,
    retry: false, // Don't retry on 404
  });
  
  // If video not found by YouTube ID, try to fetch from YouTube API and create it
  const { data: youtubeVideo, isLoading: youtubeLoading } = useQuery({
    queryKey: [`/api/youtube/video/${youtubeId}`],
    enabled: isAuthenticated && !!youtubeId && !!videoError && videoError.response?.status === 404,
    retry: false,
  });
  
  // Create video from YouTube data if it doesn't exist
  const createVideoMutation = useMutation({
    mutationFn: async (videoData: any) => {
      return await apiRequest("/api/videos", {
        method: "POST",
        body: JSON.stringify(videoData)
      });
    },
    onSuccess: () => {
      // Refetch the video after creation
      queryClient.invalidateQueries({ queryKey: [`/api/videos/youtube/${youtubeId}`] });
    },
  });
  
  // Auto-create video if we have YouTube data but no database entry
  useEffect(() => {
    if (youtubeVideo && !video && !createVideoMutation.isPending) {
      createVideoMutation.mutate({
        youtubeId: youtubeVideo.youtubeId,
        title: youtubeVideo.title,
        description: youtubeVideo.description || '',
        thumbnailUrl: youtubeVideo.thumbnailUrl,
        duration: youtubeVideo.duration || 'PT0S',
        channelTitle: youtubeVideo.channelTitle,
        publishedAt: youtubeVideo.publishedAt,
        viewCount: youtubeVideo.viewCount || 0,
      });
    }
  }, [youtubeVideo, video, createVideoMutation]);
  
  const finalVideo = tempVideo || video || youtubeVideo;
  const finalLoading = !tempVideo && (videoLoading || youtubeLoading || createVideoMutation.isPending);

  const { data: progress, refetch: refetchProgress } = useQuery({
    queryKey: finalVideo ? [`/api/video-progress/${finalVideo.id}`] : [],
    enabled: isAuthenticated && !!finalVideo?.id,
    staleTime: 10000, // Consider data fresh for 10 seconds
    cacheTime: 30000, // Cache for 30 seconds
    refetchOnWindowFocus: false, // Don't refetch on window focus
  });

  const updateProgressMutation = useMutation({
    mutationFn: async (progressData: { videoId: number; currentTime: number; isCompleted: boolean }) => {
      console.log('🔄 Updating progress:', progressData);
      try {
        const response = await apiRequest("PUT", "/api/video-progress", progressData);
        console.log('✅ Progress API response:', response);
        return response;
      } catch (error) {
        console.error('❌ Progress API error:', error);
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      console.log('🔄 Progress saved successfully:', {
        videoId: variables.videoId,
        currentTime: variables.currentTime,
        isCompleted: variables.isCompleted
      });
      
      // Invalidate progress queries to refresh data
      queryClient.invalidateQueries({ 
        queryKey: [`/api/video-progress/${finalVideo?.id}`],
        refetchType: 'active'
      });
      if (planData?.id) {
        console.log(`🔄 Invalidating plan progress for plan ${planData.id}`);
        queryClient.invalidateQueries({ 
          queryKey: [`/api/progress/plan/${planData.id}`],
          refetchType: 'active'
        });
      }
      console.log('🔄 Invalidating learning plans query');
      queryClient.invalidateQueries({ 
        queryKey: ['/api/learning-plans'],
        refetchType: 'active'
      });
      queryClient.invalidateQueries({ 
        queryKey: ['/api/learning-plans-with-progress'],
        refetchType: 'active'
      });
      
      // No toast messages for progress saves
    },
    onError: (error) => {
      console.error('❌ Progress update failed:', error);
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to save progress. Please try again.",
        variant: "destructive",
      });
    },
  });

  const markCompletedMutation = useMutation({
    mutationFn: async () => {
      if (!finalVideo || !finalVideo.id) {
        console.error("Video data:", finalVideo);
        throw new Error("Video not loaded properly");
      }
      
      console.log("Marking video complete:", {
        videoId: finalVideo.id,
        videoTitle: finalVideo.title,
        currentTime: currentTime
      });
      
      try {
        const response = await apiRequest("PUT", "/api/video-progress", {
          videoId: finalVideo.id,
          currentTime: Math.max(currentTime, 1),
          isCompleted: true,
        });
        console.log('✅ Mark complete API response:', response);
        return response;
      } catch (error) {
        console.error('❌ Mark complete API error:', error);
        throw error;
      }
    },
    onSuccess: async () => {
      // Immediately update local state for instant UI feedback
      setIsCompleted(true);
      
      // Refetch progress data
      await refetchProgress();
      
      // Invalidate related queries
      if (planData?.id) {
        console.log(`🎉 Video completed! Invalidating plan progress for plan ${planData.id}`);
        queryClient.invalidateQueries({ 
          queryKey: [`/api/progress/plan/${planData.id}`],
          refetchType: 'active'
        });
      }
      console.log('🎉 Video completed! Invalidating learning plans and continue learning queries');
      queryClient.invalidateQueries({ 
        queryKey: ['/api/continue-learning'],
        refetchType: 'active'
      });
      queryClient.invalidateQueries({ 
        queryKey: ['/api/learning-plans'],
        refetchType: 'active'
      });
      queryClient.invalidateQueries({ 
        queryKey: ['/api/learning-plans-with-progress'],
        refetchType: 'active'
      });
      
      toast({
        title: "Congratulations!",
        description: "Video marked as completed",
      });
      
      // Trigger custom event for other components to refresh
      window.dispatchEvent(new CustomEvent('videoCompleted', {
        detail: { videoId: finalVideo.id, planId: planData?.id }
      }));
      
      // Force invalidate all related queries immediately
      setTimeout(() => {
        console.log('🔄 Force invalidating all progress queries');
        queryClient.invalidateQueries({ queryKey: ['/api/learning-plans'] });
        queryClient.invalidateQueries({ queryKey: ['/api/learning-plans-with-progress'] });
        queryClient.refetchQueries({ queryKey: ['/api/learning-plans-with-progress'] });
      }, 1000);
    },
    onError: (error) => {
      console.error("Mark complete error:", error);
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to mark video as complete. Please try again.",
        variant: "destructive",
      });
    },
  });





  // Set initial currentTime from progress and track completion status
  useEffect(() => {
    if (progress?.currentTime && progress.currentTime > 0) {
      console.log(`🔄 Loading saved progress: ${progress.currentTime}s`);
      setCurrentTime(progress.currentTime);
    }
    setIsCompleted(progress?.isCompleted || false);
    
    console.log('📊 Progress loaded:', {
      savedTime: progress?.currentTime,
      isCompleted: progress?.isCompleted,
      videoId: finalVideo?.id
    });
  }, [progress?.currentTime, progress?.isCompleted, finalVideo?.id]);

  // Track initial video view for history (only once per video)
  useEffect(() => {
    if (finalVideo?.id && isAuthenticated && !progress) {
      const trackingData = {
        videoId: finalVideo.id,
        currentTime: 0,
        isCompleted: false,
      };
      
      fetch('/api/video-progress', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(trackingData),
      }).catch(() => {
        // Tracking failed, continue silently
      });
    }
  }, [finalVideo?.id, isAuthenticated, progress]);



  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isLoading || finalLoading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-600">Loading video...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !finalVideo) {
    return null;
  }

  const progressPercentage = progress?.isCompleted ? 100 : (duration > 0 && currentTime > 0 ? Math.min((currentTime / duration) * 100, 100) : 0);
  
  console.log('📊 Progress calculation:', {
    currentTime,
    duration,
    progressPercentage: progressPercentage.toFixed(1),
    isCompleted: progress?.isCompleted
  });

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <div className="bg-white border-b px-4 py-3">
        <div className="flex items-center justify-between">
          <Link href={planSlug ? `/plan/${planSlug}` : "/"}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              {planSlug ? "Back to Plan" : "Back"}
            </Button>
          </Link>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => {
                if (finalVideo?.id) {
                  console.log(`💾 Manual save: ${Math.floor(currentTime)}s`);
                  updateProgressMutation.mutate({
                    videoId: finalVideo.id,
                    currentTime: Math.floor(currentTime),
                    isCompleted: false,
                  });
                }
              }}
              disabled={updateProgressMutation.isPending}
            >
              {updateProgressMutation.isPending ? "Saving..." : "Save Progress"}
            </Button>

            <Button 
              variant="default" 
              size="sm"
              onClick={() => markCompletedMutation.mutate()}
              disabled={markCompletedMutation.isPending || isCompleted}
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              {markCompletedMutation.isPending ? "Marking..." : isCompleted ? "Completed" : "Mark Complete"}
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-col lg:flex-row gap-4 p-4 max-w-7xl mx-auto">
        {/* Video Player Section */}
        <div className="flex-1">
          {/* YouTube Player */}
          <div className="bg-black rounded-lg overflow-hidden mb-4">
            <YouTubePlayer
              key={`${finalVideo.youtubeId}-${finalVideo.id}`}
              youtubeId={finalVideo.youtubeId}
              title={finalVideo.title}
              startTime={progress?.currentTime || 0}
              onTimeUpdate={(time) => {
                if (time > 0 && Math.abs(time - currentTime) > 5) {
                  setCurrentTime(time);
                  
                  // Calculate progress percentage
                  const progressPercent = duration > 0 ? (time / duration) * 100 : 0;
                  
                  // Auto-complete when reaching 95% or more
                  if (progressPercent >= 95 && !isCompleted && finalVideo?.id) {
                    console.log(`🎯 Video ${progressPercent.toFixed(1)}% complete, marking as finished`);
                    markCompletedMutation.mutate();
                    return;
                  }
                  
                  // Auto-save progress every 10 seconds
                  if (finalVideo?.id && Math.floor(time) % 10 === 0) {
                    console.log(`💾 Auto-saving progress: ${Math.floor(time)}s`);
                    updateProgressMutation.mutate({
                      videoId: finalVideo.id,
                      currentTime: Math.floor(time),
                      isCompleted: false,
                    });
                  }
                }
              }}
              onDurationChange={(videoDuration) => {
                console.log('📏 Video duration received:', videoDuration);
                if (videoDuration > 0) {
                  setDuration(videoDuration);
                }
              }}
              onEnded={() => {
                // Only auto-complete if not already completed
                if (finalVideo?.id && !isCompleted) {
                  console.log('🎬 Video ended naturally, marking complete');
                  markCompletedMutation.mutate();
                }
              }}
              className="w-full h-auto"
            />
          </div>

          {/* Add to Plan Button Below Player */}
          <div className="mb-4 flex justify-end">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Add to Plan
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add to Learning Plan</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">
                    Add "{finalVideo?.title}" to a learning plan
                  </p>
                  <Select onValueChange={(planId) => {
                    if (finalVideo && planId) {
                      fetch(`/api/learning-plans/${planId}/videos`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ 
                          videoId: finalVideo.id,
                          orderIndex: 0
                        })
                      }).then(async response => {
                        const data = await response.json();
                        if (response.ok) {
                          if (data.code === 'ALREADY_EXISTS') {
                            toast({ 
                              title: "Already Added", 
                              description: "This video is already in the selected plan",
                              variant: "default"
                            });
                          } else {
                            toast({ title: "Success", description: "Video added to plan" });
                          }
                        } else {
                          toast({ title: "Error", description: data.message || "Failed to add video", variant: "destructive" });
                        }
                      });
                    }
                  }}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a learning plan" />
                    </SelectTrigger>
                    <SelectContent>
                      {userPlans.map((plan: any) => (
                        <SelectItem key={plan.id} value={plan.id.toString()}>
                          {plan.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Auto-play Countdown Overlay */}
          {showAutoPlayCountdown && (
            <div className="bg-white rounded-lg p-4 mb-4 border-l-4 border-blue-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-slate-800">Next video starting in {countdown} seconds...</h3>
                  <p className="text-sm text-slate-600">Auto-play is enabled</p>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    setShowAutoPlayCountdown(false);
                    if ((window as any).autoPlayInterval) {
                      clearInterval((window as any).autoPlayInterval);
                    }
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}

          {/* Video Info */}
          <div className="bg-white rounded-lg p-4 mb-4">
            <h1 className="text-xl font-bold text-slate-800 mb-2">{finalVideo.title}</h1>
            <div className="flex flex-wrap items-center gap-2 text-sm text-slate-600 mb-3">
              <span className="font-medium">{finalVideo.channelTitle}</span>
              <span>•</span>
              <span>{finalVideo.viewCount?.toLocaleString()} views</span>
              <span>•</span>
              <span>{new Date(finalVideo.publishedAt).toLocaleDateString()}</span>
            </div>
            
            {progress && (
              <div className="mb-3">
                <div className="flex items-center justify-between text-sm mb-1">
                  <span className="text-slate-600">Your Progress</span>
                  <span className="font-medium text-slate-800">
                    {isCompleted ? 'Completed' : `${Math.round(progressPercentage)}%`}
                  </span>
                </div>
                <Progress value={isCompleted ? 100 : progressPercentage} className="h-2" />
                <div className="flex justify-end mt-2">
                  <VideoHeartButton videoId={finalVideo.id} />
                </div>
              </div>
            )}
            
            <p className="text-slate-700 text-sm leading-relaxed">{finalVideo.description}</p>
          </div>
          
          {/* Related Videos */}
          <RelatedVideos currentVideoId={finalVideo.id} planSlug={planSlug} />
        </div>

        {/* Playlist Sidebar - Desktop */}
        <div className="hidden lg:block w-80">
          <PlaylistSidebar 
            planSlug={planSlug}
            currentVideoId={finalVideo.id}
            autoPlayEnabled={autoPlayEnabled}
            onAutoPlayChange={(enabled) => {
              setAutoPlayEnabled(enabled);
              localStorage.setItem('autoPlayEnabled', JSON.stringify(enabled));
            }}
            onVideoSelect={(videoId) => {
              // Navigate to new video with proper reload
              const newUrl = `/video/${videoId}${planSlug ? `?plan=${planSlug}` : ''}`;
              window.location.href = newUrl;
            }}
          />
        </div>
      </div>

      {/* Playlist Section - Mobile */}
      <div className="lg:hidden px-4 pb-4">
        <PlaylistSidebar 
          planSlug={planSlug}
          currentVideoId={finalVideo.id}
          autoPlayEnabled={autoPlayEnabled}
          onAutoPlayChange={(enabled) => {
            setAutoPlayEnabled(enabled);
            localStorage.setItem('autoPlayEnabled', JSON.stringify(enabled));
          }}
          onVideoSelect={(videoId) => {
            const newUrl = `/video/${videoId}${planSlug ? `?plan=${planSlug}` : ''}`;
            window.location.href = newUrl;
          }}
          isMobile={true}
        />
      </div>
    </div>
  );
}
