import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { GraduationCap, Play, Target, TrendingUp, Heart } from "lucide-react";

export default function Landing() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-violet-50">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-sm border-b border-slate-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="h-16 flex items-center justify-center">
                <img 
                  src="https://learniify-logo.s3.ap-south-1.amazonaws.com/logo.png" 
                  alt="Learniify Logo" 
                  className="h-10 w-auto object-contain max-w-[128px]"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.nextElementSibling?.classList.remove('hidden');
                  }}
                />
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-violet-500 rounded-lg flex items-center justify-center hidden">
                  <GraduationCap className="text-white w-6 h-6" />
                </div>
              </div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-500 via-violet-500 to-emerald-500 bg-clip-text text-transparent">Learniify</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Button 
                variant="outline"
                onClick={() => window.location.href = '/login'}
              >
                Sign In
              </Button>
              <Button 
                onClick={() => window.location.href = '/signup'}
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                Sign Up
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-slate-800 mb-6">
            Your personalized
            <span className="bg-gradient-to-r from-blue-500 to-violet-500 bg-clip-text text-transparent">
              {" "}learning journey{" "}
            </span>
            starts here
          </h1>
          <p className="text-xl text-slate-600 mb-8 max-w-3xl mx-auto">
            Create custom learning plans, discover YouTube content, track your progress, 
            and achieve your educational goals with our comprehensive learning platform.
          </p>
          <div className="flex items-center justify-center space-x-4">
            <Button 
              size="lg"
              onClick={() => window.location.href = '/signup'}
              className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-4 text-lg"
            >
              Get Started for Free
            </Button>
            <Button 
              size="lg"
              variant="outline"
              onClick={() => window.location.href = '/login'}
              className="px-8 py-4 text-lg"
            >
              Sign In
            </Button>
          </div>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-8 mt-20">
          <Card className="border-0 shadow-lg">
            <CardContent className="p-8 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Target className="text-blue-500 w-6 h-6" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Custom Learning Plans</h3>
              <p className="text-slate-600">
                Design personalized learning paths tailored to your goals and interests.
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg">
            <CardContent className="p-8 text-center">
              <div className="w-12 h-12 bg-violet-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Play className="text-violet-500 w-6 h-6" />
              </div>
              <h3 className="text-xl font-semibold mb-3">YouTube Integration</h3>
              <p className="text-slate-600">
                Search and embed educational videos directly into your learning plans.
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg">
            <CardContent className="p-8 text-center">
              <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="text-emerald-500 w-6 h-6" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Progress Tracking</h3>
              <p className="text-slate-600">
                Monitor your learning journey with detailed analytics and achievements.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Skills Section */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-800 mb-4">
              Master In-Demand Skills
            </h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Explore learning paths across the most sought-after skill categories
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              "Business and Management",
              "Health and Wellness", 
              "Technology and Computer Science",
              "Graphic Design and Arts",
              "Personal Development",
              "Languages",
              "Marketing and Sales",
              "Data Science and Analytics"
            ].map((skill, index) => (
              <Card key={index} className="border-0 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer bg-gradient-to-br from-blue-500 via-violet-500 to-emerald-500 text-white hover:scale-105">
                <CardContent className="p-4 text-center">
                  <h3 className="font-medium text-white text-sm">{skill}</h3>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Donation Banner - Controlled by VITE_ENABLE_DONATIONS */}
        {import.meta.env.VITE_ENABLE_DONATIONS === 'true' && (
          <div className="mt-20 bg-gradient-to-r from-red-500 to-pink-600 rounded-2xl p-8 text-center text-white">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Heart className="w-6 h-6" fill="currentColor" />
              <h2 className="text-2xl font-bold">
                Keep Learniify Free for Everyone
              </h2>
            </div>
            <p className="text-red-100 mb-6 max-w-2xl mx-auto">
              Your support helps us maintain servers, develop new features, and reach learners who need it most.
            </p>
            <Button
              size="lg"
              className="bg-white text-red-600 hover:bg-gray-100 px-8 py-3 text-lg font-semibold"
              onClick={() => window.location.href = '/donate'}
            >
              <Heart className="mr-2 w-5 h-5" fill="currentColor" />
              Support Our Mission
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
