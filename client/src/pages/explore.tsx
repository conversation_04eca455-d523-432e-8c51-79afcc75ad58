import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import SharePlanModal from "@/components/share-plan-modal";
import { PlanLikeButton } from "@/components/plan-like-button";
import {
  Search,
  Heart,
  Plus,
  User,
  Calendar,
  Play,
  BookOpen,
  Globe,
  TrendingUp,
  Share2
} from "lucide-react";

interface PublicPlan {
  id: number;
  title: string;
  description: string;
  slug: string;
  createdAt: string;
  userId: string;
  author: {
    firstName: string;
    lastName: string;
    email: string;
  };
}

export default function Explore() {
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(1);
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: publicPlans = [], isLoading } = useQuery({
    queryKey: ["/api/public-plans", page, searchQuery],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "12"
      });
      if (searchQuery) params.append("search", searchQuery);
      
      return await apiRequest(`/api/public-plans?${params}`);
    },
  });

  const addPlanMutation = useMutation({
    mutationFn: async (planId: number) => {
      return await apiRequest(`/api/shared-plan/public/${planId}/accept`, {
        method: "POST"
      });
    },
    onSuccess: () => {
      toast({
        title: "Plan Added!",
        description: "The plan has been added to your library",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/learning-plans"] });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to add plan to your library",
        variant: "destructive",
      });
    },
  });

  const handleSearch = () => {
    setPage(1);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const getAuthorName = (author: PublicPlan['author']) => {
    if (author.firstName && author.lastName) {
      return `${author.firstName} ${author.lastName}`;
    }
    return author.email.split('@')[0];
  };

  const getInitials = (author: PublicPlan['author']) => {
    if (author.firstName && author.lastName) {
      return `${author.firstName[0]}${author.lastName[0]}`.toUpperCase();
    }
    return author.email[0].toUpperCase();
  };

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
              <Globe className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Explore Community Plans</h1>
              <p className="text-gray-600">
                Discover and learn from learning plans shared by the Learniify community
              </p>
            </div>
          </div>

          {/* Search Bar */}
          <div className="flex gap-4 max-w-2xl">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <Input
                type="text"
                placeholder="Search public learning plans..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                className="pl-10"
              />
            </div>
            <Button onClick={handleSearch} className="bg-blue-500 hover:bg-blue-600">
              <Search className="w-4 h-4 mr-2" />
              Search
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{publicPlans.length}</div>
              <div className="text-sm text-gray-600">Public Plans</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                <TrendingUp className="w-6 h-6 mx-auto mb-1" />
              </div>
              <div className="text-sm text-gray-600">Growing Community</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-purple-600">
                <BookOpen className="w-6 h-6 mx-auto mb-1" />
              </div>
              <div className="text-sm text-gray-600">Free Learning</div>
            </CardContent>
          </Card>
        </div>

        {/* Plans Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-3 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3 mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : publicPlans.length === 0 ? (
          <div className="text-center py-12">
            <Globe className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              {searchQuery ? "No plans found" : "No public plans yet"}
            </h3>
            <p className="text-gray-600">
              {searchQuery 
                ? "Try a different search term or browse all plans"
                : "Be the first to share a learning plan with the community!"
              }
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {publicPlans.map((plan: PublicPlan) => (
              <Card key={plan.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg line-clamp-2 mb-2">
                        {plan.title}
                      </CardTitle>
                      <p className="text-sm text-gray-600 line-clamp-2">
                        {plan.description || "No description provided"}
                      </p>
                    </div>
                    <Badge variant="secondary" className="ml-2">
                      <Globe className="w-3 h-3 mr-1" />
                      Public
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Author Info */}
                  <div className="flex items-center gap-3 mb-4">
                    <Avatar className="w-8 h-8">
                      <AvatarFallback className="text-xs">
                        {getInitials(plan.author)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="text-sm font-medium">
                        {getAuthorName(plan.author)}
                      </div>
                      <div className="text-xs text-gray-500 flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {new Date(plan.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      className="flex-1 bg-blue-500 hover:bg-blue-600"
                      onClick={() => window.open(`/plan/${plan.slug}`, '_blank')}
                    >
                      <Play className="w-4 h-4 mr-2" />
                      View
                    </Button>
                    {isAuthenticated && (
                      <>
                        <PlanLikeButton planId={plan.id} size="sm" />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => addPlanMutation.mutate(plan.id)}
                          disabled={addPlanMutation.isPending}
                          className="bg-green-50 text-green-700 hover:bg-green-100 border-green-200"
                          title="Add to My Plans"
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                        <SharePlanModal planId={plan.id} planTitle={plan.title}>
                          <Button
                            size="sm"
                            variant="outline"
                            className="bg-purple-50 text-purple-700 hover:bg-purple-100 border-purple-200"
                            title="Share Plan"
                          >
                            <Share2 className="w-4 h-4" />
                          </Button>
                        </SharePlanModal>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Load More */}
        {publicPlans.length > 0 && publicPlans.length >= 12 && (
          <div className="text-center mt-8">
            <Button
              variant="outline"
              onClick={() => setPage(page + 1)}
              disabled={isLoading}
            >
              Load More Plans
            </Button>
          </div>
        )}

        {/* Call to Action */}
        {isAuthenticated && (
          <Card className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
            <CardContent className="p-6 text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                Share Your Knowledge
              </h3>
              <p className="text-gray-600 mb-4">
                Help others learn by making your learning plans public. 
                Go to "My Plans" and toggle the visibility of any plan.
              </p>
              <Button 
                onClick={() => window.location.href = '/plans'}
                className="bg-blue-500 hover:bg-blue-600"
              >
                <BookOpen className="w-4 h-4 mr-2" />
                Go to My Plans
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}