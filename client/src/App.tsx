import { Switch, Route, useLocation } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { useAuth } from "@/hooks/useAuth";
import { useActivityTracker } from "@/hooks/useActivityTracker";
import { useEffect } from "react";
import Layout from "@/components/layout";
import NotFound from "@/pages/not-found";
import Landing from "@/pages/landing";
import LoginPage from "@/pages/login";
import SignupPage from "@/pages/signup";
import ForgotPassword from "@/pages/forgot-password";
import ResetPassword from "@/pages/reset-password";
import Dashboard from "@/pages/dashboard";
import MyPlans from "@/pages/my-plans";
import Explore from "@/pages/explore";
import Progress from "@/pages/progress";
import LearningPlan from "@/pages/learning-plan";
import VideoPlayer from "@/pages/video-player";
import Library from "@/pages/library";
import SharedPlan from "@/pages/shared-plan";
import InvitePage from "@/pages/invite";
import ContactPage from "@/pages/contact";
import PrivacyPolicy from "@/pages/privacy-policy";
import TermsOfService from "@/pages/terms-of-service";
import Profile from "@/pages/profile";
import FAQ from "@/pages/faq";
import CookiePolicy from "@/pages/cookie-policy";
import Security from "@/pages/security";
import AdminLogin from "@/pages/admin-login";
import AdminDashboard from "@/pages/admin-dashboard";
import DonatePage from "@/pages/donate";
import LegalDisclaimer from "@/pages/legal-disclaimer";

function Router() {
  const { isAuthenticated, isLoading } = useAuth();
  const [location] = useLocation();
  
  // Track user activity for auto-logout
  useActivityTracker();
  
  // Scroll to top on route change (mobile fix)
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'instant' });
  }, [location]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <Switch>
        <Route path="/" component={Landing} />
        <Route path="/login" component={LoginPage} />
        <Route path="/signup" component={SignupPage} />
        <Route path="/forgot-password" component={ForgotPassword} />
        <Route path="/reset-password" component={ResetPassword} />
        <Route path="/shared/:shareToken" component={SharedPlan} />
        <Route path="/invite/:inviteToken" component={InvitePage} />
        <Route path="/contact" component={ContactPage} />
        <Route path="/privacy-policy" component={PrivacyPolicy} />
        <Route path="/terms-of-service" component={TermsOfService} />
        <Route path="/faq" component={FAQ} />
        <Route path="/cookie-policy" component={CookiePolicy} />
        <Route path="/security" component={Security} />
        {localStorage.getItem('enableDonations') === 'true' && (
          <Route path="/donate" component={DonatePage} />
        )}
        <Route path="/legal" component={LegalDisclaimer} />
        <Route path="/admin/login" component={AdminLogin} />
        <Route path="/admin/dashboard" component={AdminDashboard} />
        <Route component={LoginPage} />
      </Switch>
    );
  }

  return (
    <Layout>
      <Switch>
        <Route path="/" component={Dashboard} />
        <Route path="/dashboard" component={Dashboard} />
        <Route path="/progress" component={Progress} />
        <Route path="/explore" component={Explore} />
        <Route path="/plans" component={MyPlans} />
        <Route path="/plan/:slug" component={LearningPlan} />
        <Route path="/video/:id" component={VideoPlayer} />
        <Route path="/library" component={Library} />
        <Route path="/shared/:shareToken" component={SharedPlan} />
        <Route path="/invite/:inviteToken" component={InvitePage} />
        <Route path="/contact" component={ContactPage} />
        <Route path="/privacy-policy" component={PrivacyPolicy} />
        <Route path="/terms-of-service" component={TermsOfService} />
        <Route path="/faq" component={FAQ} />
        <Route path="/cookie-policy" component={CookiePolicy} />
        <Route path="/security" component={Security} />
        <Route path="/profile" component={Profile} />
        {localStorage.getItem('enableDonations') === 'true' && (
          <Route path="/donate" component={DonatePage} />
        )}
        <Route path="/legal" component={LegalDisclaimer} />
        <Route path="/admin/login" component={AdminLogin} />
        <Route path="/admin/dashboard" component={AdminDashboard} />
        <Route component={Dashboard} />
      </Switch>
    </Layout>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <TooltipProvider>
          <Toaster />
          <Router />
        </TooltipProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
