import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Play, Plus, Clock, Eye, Sparkles } from "lucide-react";
import { Link } from "wouter";

interface RelatedVideo {
  id: number;
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  duration: string;
  channelTitle: string;
  viewCount?: number;
  publishedAt: string;
  relevanceScore: number;
}

interface RelatedVideosProps {
  currentVideoId: number;
  planSlug?: string;
}

export default function RelatedVideos({ currentVideoId, planSlug }: RelatedVideosProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isCreatingPlan, setIsCreatingPlan] = useState(false);
  const [newPlanTitle, setNewPlanTitle] = useState("");

  // Get user's learning plans for adding videos
  const { data: userPlans = [], refetch: refetchPlans, isLoading: plansLoading, error: plansError } = useQuery({
    queryKey: ["/api/learning-plans"],
    staleTime: 0, // Always fetch fresh data
    cacheTime: 0, // Don't cache the data
  });
  
  // Debug: Log current plans
  console.log('📋 Plans Query Debug:', { 
    userPlans, 
    plansCount: userPlans.length, 
    isLoading: plansLoading, 
    error: plansError 
  });

  // Get related videos
  const { data: relatedVideos = [], isLoading, error } = useQuery({
    queryKey: [`/api/videos/${currentVideoId}/related`],
    enabled: !!currentVideoId,
    retry: false,
  });
  
  // Auto-create related videos in DB
  const createRelatedVideosMutation = useMutation({
    mutationFn: async (videos: any[]) => {
      const createdVideos = [];
      for (const video of videos) {
        try {
          const createdVideo = await apiRequest("/api/videos", {
            method: "POST",
            body: JSON.stringify({
              youtubeId: video.youtubeId,
              title: video.title,
              description: video.description || '',
              thumbnailUrl: video.thumbnailUrl,
              duration: video.duration || 'PT0S',
              channelTitle: video.channelTitle,
              publishedAt: video.publishedAt,
              viewCount: video.viewCount || 0,
            })
          });
          createdVideos.push(createdVideo);
        } catch (error) {
          // Video creation failed, continue with others
        }
      }
      return createdVideos;
    },
  });
  
  // Auto-create related videos when they load
  useEffect(() => {
    if (relatedVideos.length > 0 && !createRelatedVideosMutation.isPending) {
      const videosToCreate = relatedVideos.filter(v => !v.id); // Only create videos without DB IDs
      if (videosToCreate.length > 0) {
        createRelatedVideosMutation.mutate(videosToCreate);
      }
    }
  }, [relatedVideos]);
  
  // Debug logging
  console.log('🔗 Related Videos Debug:', {
    currentVideoId,
    relatedVideos: relatedVideos.length,
    isLoading,
    error,
    planSlug
  });
  
  // Always show component for debugging
  console.log('🔗 RelatedVideos component is rendering for video:', currentVideoId);

  // Create new plan mutation
  const createPlanMutation = useMutation({
    mutationFn: async (title: string) => {
      try {
        console.log('📝 Creating plan with title:', title);
        const response = await fetch("/api/learning-plans", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({
            title: title.trim(),
            description: `Learning plan created for organizing videos`,
            isPublic: false,
          }),
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        console.log('📝 Raw response:', response);
        
        // Parse the JSON response
        const data = await response.json();
        
        console.log('✅ Plan creation data:', data);
        return data;
      } catch (error) {
        console.error('❌ Plan creation error:', error);
        throw error;
      }
    },
    onSuccess: (response, title) => {
      console.log('✅ Plan created, invalidating queries...');
      
      // Invalidate and refetch queries to update the UI
      queryClient.invalidateQueries({ queryKey: ["/api/learning-plans"] });
      
      // Force immediate refetch
      refetchPlans();
      
      // Also add a delayed refetch as backup
      setTimeout(() => {
        refetchPlans();
      }, 500);
      
      setIsCreatingPlan(false);
      setNewPlanTitle("");
      
      // Use the title from the input since response might not have the expected format
      const planTitle = response?.title || title;
      toast({
        title: "Plan Created",
        description: `"${planTitle}" has been created successfully`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: "Failed to create plan. Please try again.",
        variant: "destructive",
      });
    },
  });



  // Add video to plan mutation
  const addToPlanMutation = useMutation({
    mutationFn: async ({ video, planId }: { video: RelatedVideo; planId: number }) => {
      // Video should already exist in DB from auto-creation, get it by YouTube ID
      let existingVideo;
      try {
        existingVideo = await apiRequest(`/api/videos/youtube/${video.youtubeId}`);
      } catch (error) {
        // If not found, create it
        existingVideo = await apiRequest("/api/videos", {
          method: "POST",
          body: JSON.stringify({
            youtubeId: video.youtubeId,
            title: video.title,
            description: video.description,
            thumbnailUrl: video.thumbnailUrl,
            duration: video.duration,
            channelTitle: video.channelTitle,
            viewCount: video.viewCount,
            publishedAt: video.publishedAt,
          })
        });
      }

      // Use fetch API like video player for proper response handling
      const response = await fetch(`/api/learning-plans/${planId}/videos`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          videoId: existingVideo.id,
          orderIndex: 0
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to add video');
      }
      
      return { data, planId };
    },
    onSuccess: (result) => {
      const plan = userPlans.find(p => p.id === result.planId);
      
      if (result.data.code === 'ALREADY_EXISTS') {
        toast({
          title: "Already Added",
          description: `This video is already in "${plan?.title}"`,
          variant: "default"
        });
      } else {
        toast({
          title: "Success",
          description: `Video added to "${plan?.title}"`,
        });
      }
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: [`/api/learning-plans/${result.planId}/videos`] });
      queryClient.invalidateQueries({ queryKey: ["/api/learning-plans"] });
    },
    onError: (error: any) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "Please log in to add videos",
          variant: "destructive",
        });
        return;
      }
      
      toast({
        title: "Error",
        description: "Failed to add video to plan",
        variant: "destructive",
      });
    },
  });

  const formatDuration = (duration: string) => {
    if (!duration || duration === 'Unknown') return 'Unknown';
    
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return duration;
    
    const hours = parseInt(match[1]) || 0;
    const minutes = parseInt(match[2]) || 0;
    const seconds = parseInt(match[3]) || 0;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatViewCount = (count?: number) => {
    if (!count) return "0 views";
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M views`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K views`;
    return `${count} views`;
  };

  if (isLoading) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-blue-500" />
            Related Videos
            <Badge variant="outline" className="ml-2">
              Loading...
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="animate-pulse">
                <div className="aspect-video bg-gray-200 rounded-t-lg"></div>
                <CardContent className="p-3">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (relatedVideos.length === 0 && !isLoading) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-blue-500" />
            Related Videos
            <Badge variant="outline" className="ml-2">
              No suggestions found
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600">
            No related videos found for this content. Try watching more videos to improve recommendations.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-blue-500" />
          Related Videos
          <Badge variant="outline" className="ml-2">
            {relatedVideos.length} suggestions
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {relatedVideos.map((video: RelatedVideo) => (
            <Card key={video.id || video.youtubeId} className="overflow-hidden hover:shadow-md transition-shadow">
              <div className="aspect-video relative">
                <img
                  src={video.thumbnailUrl}
                  alt={video.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                  <Link href={`/video/${video.youtubeId}${planSlug ? `?plan=${planSlug}` : ''}`}>
                    <Button size="sm" className="bg-white text-black hover:bg-gray-100">
                      <Play className="w-3 h-3" />
                    </Button>
                  </Link>
                </div>
                {video.duration !== 'Unknown' && (
                  <div className="absolute bottom-1 right-1 bg-black bg-opacity-75 text-white px-1 py-0.5 rounded text-xs">
                    {formatDuration(video.duration)}
                  </div>
                )}
                {video.isFromYouTube && (
                  <div className="absolute top-1 left-1">
                    <Badge className="bg-red-500 text-white text-xs">
                      YouTube
                    </Badge>
                  </div>
                )}
              </div>
              <CardContent className="p-3">
                <h3 className="font-medium text-sm mb-1 line-clamp-2" title={video.title}>
                  {video.title}
                </h3>
                <p className="text-xs text-gray-500 mb-2">{video.channelTitle}</p>
                <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                  {video.viewCount > 0 && (
                    <div className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      {formatViewCount(video.viewCount)}
                    </div>
                  )}
                  {video.duration !== 'Unknown' && (
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {formatDuration(video.duration)}
                    </div>
                  )}
                  {video.isFromYouTube && (
                    <div className="flex items-center gap-1 text-red-600">
                      <Sparkles className="w-3 h-3" />
                      Fresh from YouTube
                    </div>
                  )}
                </div>
                
                <div className="flex gap-1">
                  <Link href={`/video/${video.youtubeId}${planSlug ? `?plan=${planSlug}` : ''}`} className="flex-1">
                    <Button size="sm" className="w-full text-xs h-7">
                      <Play className="w-3 h-3 mr-1" />
                      Watch
                    </Button>
                  </Link>
                  
                  <Select
                    onValueChange={(value) => {
                      if (value === "create-new") {
                        setIsCreatingPlan(true);
                      } else {
                        addToPlanMutation.mutate({ video, planId: parseInt(value) });
                      }
                    }}
                  >
                    <SelectTrigger className="w-16 h-7 text-xs">
                      <Plus className="w-3 h-3" />
                    </SelectTrigger>
                    <SelectContent>
                      {userPlans.map((plan: any) => (
                        <SelectItem key={plan.id} value={plan.id.toString()}>
                          {plan.title}
                        </SelectItem>
                      ))}
                      <SelectItem value="create-new" className="text-blue-600 font-medium">
                        + Create New Plan
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
      
      {/* Create New Plan Dialog */}
      {isCreatingPlan && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-[90vw]">
            <h3 className="text-lg font-semibold mb-4">Create New Learning Plan</h3>
            <Input
              placeholder="Enter plan title (e.g., React Fundamentals)"
              value={newPlanTitle}
              onChange={(e) => setNewPlanTitle(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && newPlanTitle.trim()) {
                  createPlanMutation.mutate(newPlanTitle);
                }
              }}
              className="mb-4"
              autoFocus
            />
            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  setIsCreatingPlan(false);
                  setNewPlanTitle("");
                }}
                disabled={createPlanMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={() => createPlanMutation.mutate(newPlanTitle)}
                disabled={!newPlanTitle.trim() || createPlanMutation.isPending}
              >
                {createPlanMutation.isPending ? "Creating..." : "Create Plan"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
}