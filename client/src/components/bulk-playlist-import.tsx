import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, <PERSON>alogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Upload, Download, Loader2 } from 'lucide-react';

interface PlaylistImportProps {
  children: React.ReactNode;
}

export default function BulkPlaylistImport({ children }: PlaylistImportProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [playlistUrl, setPlaylistUrl] = useState('');
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const importMutation = useMutation({
    mutationFn: async (url: string) => {
      return await apiRequest('/api/youtube/import-playlist', {
        method: 'POST',
        body: JSON.stringify({ playlistUrl: url })
      });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/learning-plans'] });
      const durationText = data.totalDuration ? ` (${data.totalDuration} total)` : '';
      toast({
        title: 'Playlist Imported!',
        description: `Successfully imported "${data.title}" with ${data.videoCount} videos${durationText}`,
      });
      setIsOpen(false);
      setPlaylistUrl('');
    },
    onError: (error: any) => {
      toast({
        title: 'Import Failed',
        description: error.message || 'Failed to import playlist',
        variant: 'destructive',
      });
    },
  });

  const handleImport = () => {
    if (!playlistUrl.trim()) {
      toast({
        title: 'URL Required',
        description: 'Please enter a YouTube playlist URL',
        variant: 'destructive',
      });
      return;
    }

    // Validate YouTube playlist URL
    const playlistRegex = /(?:youtube\.com\/playlist\?list=|youtu\.be\/playlist\?list=)([a-zA-Z0-9_-]+)/;
    if (!playlistRegex.test(playlistUrl)) {
      toast({
        title: 'Invalid URL',
        description: 'Please enter a valid YouTube playlist URL',
        variant: 'destructive',
      });
      return;
    }

    importMutation.mutate(playlistUrl);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="w-5 h-5 text-blue-500" />
            Import Playlist
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
              YouTube Playlist URL
            </label>
            <Input
              placeholder="https://youtube.com/playlist?list=..."
              value={playlistUrl}
              onChange={(e) => setPlaylistUrl(e.target.value)}
              className="mt-1"
            />
            <p className="text-xs text-slate-500 mt-1">
              Paste a playlist URL to import all videos as a learning plan
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              className="flex-1"
              onClick={() => setIsOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleImport}
              disabled={importMutation.isPending}
              className="flex-1"
            >
              {importMutation.isPending ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  Import
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}