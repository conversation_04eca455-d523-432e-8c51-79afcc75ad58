import { useEffect, useRef } from 'react';
import { useAuth } from '@/hooks/useAuth';

interface GoogleAdProps {
  slot: string;
  format?: 'auto' | 'rectangle' | 'vertical' | 'horizontal';
  style?: React.CSSProperties;
  className?: string;
  responsive?: boolean;
}

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

export function GoogleAd({ 
  slot, 
  format = 'auto', 
  style = { display: 'block' },
  className = '',
  responsive = true 
}: GoogleAdProps) {
  const { user } = useAuth();
  const adRef = useRef<HTMLDivElement>(null);

  // Check if user is a supporter (has donated)
  const isSupporter = user?.isSupporter || false;

  useEffect(() => {
    // Don't show ads to supporters
    if (isSupporter) return;

    // Load Google AdSense script if not already loaded
    if (!window.adsbygoogle) {
      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXXXXXXXXX';
      script.crossOrigin = 'anonymous';
      document.head.appendChild(script);
      
      window.adsbygoogle = [];
    }

    // Initialize ad after a short delay
    const timer = setTimeout(() => {
      try {
        if (window.adsbygoogle && adRef.current) {
          (window.adsbygoogle = window.adsbygoogle || []).push({});
        }
      } catch (error) {
        console.error('AdSense error:', error);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [isSupporter, slot]);

  // Don't render ads for supporters
  if (isSupporter) {
    return (
      <div className={`text-center p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg ${className}`}>
        <div className="text-sm text-gray-600">
          ❤️ Thank you for supporting Learniify!
        </div>
      </div>
    );
  }

  return (
    <div className={`ad-container ${className}`} ref={adRef}>
      <ins
        className="adsbygoogle"
        style={style}
        data-ad-client="ca-pub-XXXXXXXXXXXXXXXXX"
        data-ad-slot={slot}
        data-ad-format={format}
        data-full-width-responsive={responsive.toString()}
      />
    </div>
  );
}

// Predefined ad components for common placements
export function BannerAd({ className }: { className?: string }) {
  return (
    <GoogleAd
      slot="1234567890"
      format="horizontal"
      className={className}
      style={{ display: 'block', width: '100%', height: '90px' }}
    />
  );
}

export function SidebarAd({ className }: { className?: string }) {
  return (
    <GoogleAd
      slot="0987654321"
      format="rectangle"
      className={className}
      style={{ display: 'block', width: '300px', height: '250px' }}
    />
  );
}

export function MobileAd({ className }: { className?: string }) {
  return (
    <div className={`block md:hidden ${className}`}>
      <GoogleAd
        slot="1122334455"
        format="auto"
        style={{ display: 'block' }}
      />
    </div>
  );
}

export function InlineAd({ className }: { className?: string }) {
  return (
    <GoogleAd
      slot="1234567890"
      format="auto"
      className={className}
      style={{ display: 'block', margin: '20px 0' }}
    />
  );
}