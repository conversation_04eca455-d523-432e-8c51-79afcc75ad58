import NavigationHeader from "@/components/navigation-header";
import MobileNavigation from "@/components/mobile-navigation";
import Footer from "@/components/footer";
import DonationBanner from "@/components/donation-banner";

interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  return (
    <div className="min-h-screen bg-background flex flex-col transition-colors">
      <NavigationHeader />
      <main className="flex-1 pb-10">{children}</main>
      <Footer />
      <MobileNavigation />
      <DonationBanner />
    </div>
  );
}
