import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Heart, X } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";

export default function DonationBanner() {
  const { user } = useAuth();
  const [isVisible, setIsVisible] = useState(false);
  
  // Check if donations are enabled via environment variable
  const donationsEnabled = import.meta.env.VITE_ENABLE_DONATIONS === 'true';

  useEffect(() => {
    // Don't show if donations are disabled
    if (!donationsEnabled) {
      const mainElement = document.querySelector('main');
      if (mainElement) {
        mainElement.classList.remove('pb-10');
      }
      return;
    }
    
    // Don't show to supporters
    if (user?.isSupporter) {
      // Remove padding if user is supporter
      const mainElement = document.querySelector('main');
      if (mainElement) {
        mainElement.classList.remove('pb-10');
      }
      return;
    }

    // Check if banner was dismissed (stored in localStorage)
    const dismissed = localStorage.getItem('donationBannerDismissed');
    const dismissedTime = dismissed ? parseInt(dismissed) : 0;
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);

    // Show banner if not dismissed or if dismissed more than 24 hours ago
    if (!dismissed || dismissedTime < oneDayAgo) {
      setIsVisible(true);
    } else {
      // Remove padding if banner is dismissed
      const mainElement = document.querySelector('main');
      if (mainElement) {
        mainElement.classList.remove('pb-10');
      }
    }
  }, [user?.isSupporter, donationsEnabled]);

  const handleClose = () => {
    setIsVisible(false);
    localStorage.setItem('donationBannerDismissed', Date.now().toString());
    
    // Remove bottom padding from main content when banner is closed
    const mainElement = document.querySelector('main');
    if (mainElement) {
      mainElement.classList.remove('pb-10');
    }
  };

  const handleDonate = () => {
    window.open('/donate', '_blank');
  };

  if (!donationsEnabled || !isVisible || user?.isSupporter) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg border-t border-red-400">
      <div className="max-w-7xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Heart className="w-5 h-5" fill="currentColor" />
            <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
              <span className="font-medium">Keep Learniify Free for Everyone!</span>
              <span className="text-red-100 text-sm">
                Your support helps us maintain servers and develop new features.
              </span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              onClick={handleDonate}
              className="bg-white text-red-600 hover:bg-red-50 font-medium"
            >
              <Heart className="w-4 h-4 mr-2" fill="currentColor" />
              Donate Now
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleClose}
              className="text-white hover:bg-red-600 p-1"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}