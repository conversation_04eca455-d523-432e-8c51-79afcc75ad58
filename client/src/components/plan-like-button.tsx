import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { ThumbsUp } from 'lucide-react';

interface PlanLikeButtonProps {
  planId: number;
  showCount?: boolean;
  size?: 'sm' | 'default' | 'lg';
}

export function PlanLikeButton({ planId, showCount = true, size = 'sm' }: PlanLikeButtonProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get like status
  const { data: likeStatus } = useQuery({
    queryKey: [`/api/plans/${planId}/like-status`],
  });

  // Get like count
  const { data: likesData } = useQuery({
    queryKey: [`/api/plans/${planId}/likes`],
  });

  const isLiked = likeStatus?.isLiked || false;
  const totalLikes = likesData?.totalLikes || 0;

  // Toggle like mutation
  const toggleLikeMutation = useMutation({
    mutationFn: async () => {
      if (isLiked) {
        return await apiRequest(`/api/plans/${planId}/like`, {
          method: 'DELETE'
        });
      } else {
        return await apiRequest(`/api/plans/${planId}/like`, {
          method: 'POST'
        });
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/plans/${planId}/like-status`] });
      queryClient.invalidateQueries({ queryKey: [`/api/plans/${planId}/likes`] });
      
      if (!isLiked) {
        toast({
          title: "Plan Liked! 👍",
          description: "Thanks for showing your appreciation!",
        });
      }
    },
    onError: (error) => {
      console.error('Error toggling like:', error);
      toast({
        title: "Error",
        description: "Failed to update like status",
        variant: "destructive",
      });
    },
  });

  const handleToggleLike = () => {
    toggleLikeMutation.mutate();
  };

  return (
    <Button
      onClick={handleToggleLike}
      disabled={toggleLikeMutation.isPending}
      size={size}
      variant={isLiked ? "default" : "outline"}
      className={`${isLiked 
        ? 'bg-blue-500 hover:bg-blue-600 text-white' 
        : 'hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300'
      } transition-all duration-200`}
    >
      <ThumbsUp className={`w-4 h-4 ${showCount ? 'mr-2' : ''} ${isLiked ? 'fill-current' : ''}`} />
      {showCount && (
        <span className="font-medium">
          {totalLikes > 0 ? totalLikes : 'Like'}
        </span>
      )}
    </Button>
  );
}