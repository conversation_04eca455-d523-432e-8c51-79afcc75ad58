import { useState, useEffect, useRef } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { Textarea } from "@/components/ui/textarea";
import { Play, CheckCircle, Circle, Zap, Target, TrendingUp, FileText, Save } from "lucide-react";

interface LearningPath {
  id: number;
  userId: string;
  planId: number;
  pathData: {
    nodes: PathNode[];
    connections: PathConnection[];
  };
  completionPercentage: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface PathNode {
  id: number;
  videoId: number;
  position: { x: number; y: number };
  isCompleted: boolean;
  order: number;
  video: {
    id: number;
    youtubeId: string;
    title: string;
    description: string;
    thumbnailUrl: string;
    duration: string;
    channelTitle: string;
  };
}

interface PathConnection {
  from: number;
  to: number;
}

interface LearningPathVisualizerProps {
  planId: number;
  planTitle: string;
}

export default function LearningPathVisualizer({ planId, planTitle }: LearningPathVisualizerProps) {
  const [selectedNode, setSelectedNode] = useState<PathNode | null>(null);
  const [animatingNodes, setAnimatingNodes] = useState<Set<number>>(new Set());
  const [notes, setNotes] = useState("");
  const [showNotes, setShowNotes] = useState(false);
  const svgRef = useRef<SVGSVGElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch learning path
  const { data: learningPath, isLoading, error } = useQuery<LearningPath>({
    queryKey: ["/api/learning-paths", planId],
    retry: false,
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
      }
    },
  });

  // Create learning path if it doesn't exist
  const createPathMutation = useMutation({
    mutationFn: async () => {
      return await apiRequest("/api/learning-paths", {
        method: "POST",
        body: JSON.stringify({ planId }),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/learning-paths", planId] });
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to create learning path.",
        variant: "destructive",
      });
    },
  });

  // Update node completion
  const updateNodeMutation = useMutation({
    mutationFn: async ({ nodeId, isCompleted }: { nodeId: number; isCompleted: boolean }) => {
      return await apiRequest(`/api/learning-paths/nodes/${nodeId}`, {
        method: "PUT",
        body: JSON.stringify({ isCompleted }),
      });
    },
    onSuccess: (_, { nodeId }) => {
      // Add animation effect
      setAnimatingNodes(prev => new Set(prev).add(nodeId));
      setTimeout(() => {
        setAnimatingNodes(prev => {
          const newSet = new Set(prev);
          newSet.delete(nodeId);
          return newSet;
        });
      }, 1000);

      queryClient.invalidateQueries({ queryKey: ["/api/learning-paths", planId] });
      toast({
        title: "Progress Updated",
        description: "Node completion status has been updated!",
      });
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to update progress.",
        variant: "destructive",
      });
    },
  });

  // Update notes mutation
  const updateNotesMutation = useMutation({
    mutationFn: async (notesText: string) => {
      if (!learningPath) throw new Error("No learning path found");
      return await apiRequest(`/api/learning-paths/${learningPath.id}/notes`, {
        method: "PUT",
        body: JSON.stringify({ notes: notesText }),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/learning-paths", planId] });
      toast({
        title: "Notes saved",
        description: "Your notes have been saved successfully.",
      });
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to save notes.",
        variant: "destructive",
      });
    },
  });

  // Generate spiral path layout
  const generatePathLayout = (videoCount: number): { x: number; y: number }[] => {
    const positions: { x: number; y: number }[] = [];
    const centerX = 300;
    const centerY = 200;
    const maxRadius = 150;
    
    for (let i = 0; i < videoCount; i++) {
      const angle = (i / videoCount) * 4 * Math.PI; // 2 full spirals
      const radius = (i / videoCount) * maxRadius;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      positions.push({ x, y });
    }
    
    return positions;
  };

  // Format duration
  const formatDuration = (duration: string) => {
    if (!duration) return "N/A";
    const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
    if (!match) return duration;
    
    const hours = match[1] ? parseInt(match[1]) : 0;
    const minutes = match[2] ? parseInt(match[2]) : 0;
    const seconds = match[3] ? parseInt(match[3]) : 0;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Auto-create path if it doesn't exist
  useEffect(() => {
    if (!isLoading && error && error.message.includes('404') && !createPathMutation.isPending && !createPathMutation.isSuccess) {
      console.log("No learning path found, creating new one");
      createPathMutation.mutate();
    }
  }, [isLoading, error, createPathMutation.isPending, createPathMutation.isSuccess]);

  // Update notes when learningPath changes
  useEffect(() => {
    if (learningPath) {
      setNotes(learningPath.notes || "");
    }
  }, [learningPath]);

  if (isLoading || createPathMutation.isPending) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Learning Path Visualizer
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">Loading learning path...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle learning path data structure
  const nodes = learningPath?.nodes || learningPath?.pathData?.nodes || [];
  
  // Calculate completion percentage
  const completedNodes = nodes.filter(node => node.isCompleted).length || 0;
  const totalNodes = nodes.length || 0;
  const completionPercentage = totalNodes > 0 ? Math.round((completedNodes / totalNodes) * 100) : 0;
  
  if (!learningPath || nodes.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Learning Path Visualizer
          </CardTitle>
          <CardDescription>
            Visualize your learning journey with an interactive path map
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-2">No learning path available</p>
            <p className="text-sm text-gray-500">Add videos to "{planTitle}" to create your learning path</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const positions = generatePathLayout(nodes.length);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5" />
          Learning Path: {planTitle}
        </CardTitle>
        <CardDescription>
          Interactive visualization of your learning journey
        </CardDescription>
        <div className="flex items-center gap-4 mt-4">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium">{completionPercentage}% Complete</span>
          </div>
          <Progress value={completionPercentage} className="flex-1 max-w-xs" />
          <Badge variant="outline">
            {completedNodes} / {totalNodes} Videos
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowNotes(!showNotes)}
            className="ml-2"
          >
            <FileText className="h-4 w-4 mr-2" />
            Notes
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Notes Section */}
        {showNotes && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-semibold text-gray-700">Learning Path Notes</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => updateNotesMutation.mutate(notes)}
                disabled={updateNotesMutation.isPending}
              >
                {updateNotesMutation.isPending ? (
                  <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                Save
              </Button>
            </div>
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add your notes about this learning path..."
              className="min-h-24 resize-none"
            />
          </div>
        )}
        
        <div className="space-y-6">
          {/* SVG Path Visualization */}
          <div className="relative bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-4 overflow-hidden">
            <svg
              ref={svgRef}
              width="100%"
              height="400"
              viewBox="0 0 600 400"
              className="border rounded"
            >
              {/* Background Grid */}
              <defs>
                <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" strokeWidth="0.5"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />

              {/* Connection Lines */}
              {nodes.map((node, index) => {
                if (index === nodes.length - 1) return null;
                const currentPos = positions[index];
                const nextPos = positions[index + 1];
                const isPathCompleted = node.isCompleted && nodes[index + 1]?.isCompleted;
                
                return (
                  <line
                    key={`connection-${index}`}
                    x1={currentPos.x}
                    y1={currentPos.y}
                    x2={nextPos.x}
                    y2={nextPos.y}
                    stroke={isPathCompleted ? "#22c55e" : node.isCompleted ? "#3b82f6" : "#d1d5db"}
                    strokeWidth="3"
                    strokeDasharray={isPathCompleted ? "none" : "5,5"}
                    className="transition-all duration-500"
                  />
                );
              })}

              {/* Video Nodes */}
              {nodes.map((node, index) => {
                const position = positions[index];
                const isAnimating = animatingNodes.has(node.id);
                
                return (
                  <g key={node.id}>
                    {/* Node Circle */}
                    <circle
                      cx={position.x}
                      cy={position.y}
                      r={isAnimating ? "25" : "20"}
                      fill={node.isCompleted ? "#22c55e" : "#3b82f6"}
                      stroke="#ffffff"
                      strokeWidth="3"
                      className={`cursor-pointer transition-all duration-300 ${
                        selectedNode?.id === node.id ? "ring-4 ring-blue-300" : ""
                      } ${isAnimating ? "animate-pulse" : ""}`}
                      onClick={() => setSelectedNode(node)}
                    />
                    
                    {/* Node Icon */}
                    <foreignObject
                      x={position.x - 10}
                      y={position.y - 10}
                      width="20"
                      height="20"
                      className="pointer-events-none"
                    >
                      <div className="flex items-center justify-center w-full h-full">
                        {node.isCompleted ? (
                          <CheckCircle className="h-4 w-4 text-white" />
                        ) : (
                          <Play className="h-4 w-4 text-white" />
                        )}
                      </div>
                    </foreignObject>
                    
                    {/* Node Number */}
                    <text
                      x={position.x}
                      y={position.y - 30}
                      textAnchor="middle"
                      className="text-xs font-bold fill-gray-700"
                    >
                      {index + 1}
                    </text>
                  </g>
                );
              })}

              {/* Progress Wave Animation */}
              {completionPercentage > 0 && (
                <g>
                  <defs>
                    <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#22c55e" stopOpacity="0.3" />
                      <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.1" />
                    </linearGradient>
                  </defs>
                  <rect
                    x="0"
                    y="0"
                    width={`${(completionPercentage / 100) * 600}`}
                    height="400"
                    fill="url(#progressGradient)"
                    className="animate-pulse"
                  />
                </g>
              )}
            </svg>
          </div>

          {/* Selected Node Details */}
          {selectedNode && (
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="p-4">
                <div className="flex items-start gap-4">
                  <img
                    src={selectedNode.video.thumbnailUrl}
                    alt={selectedNode.video.title}
                    className="w-32 h-20 object-cover rounded"
                  />
                  <div className="flex-1">
                    <h4 className="font-semibold text-lg mb-2">{selectedNode.video.title}</h4>
                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                      <span>{selectedNode.video.channelTitle}</span>
                      <span>{formatDuration(selectedNode.video.duration)}</span>
                      <Badge variant={selectedNode.isCompleted ? "default" : "secondary"}>
                        {selectedNode.isCompleted ? "Completed" : "Not Started"}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-700 mb-4 line-clamp-2">
                      {selectedNode.video.description}
                    </p>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => window.open(`https://youtube.com/watch?v=${selectedNode.video.youtubeId}`, '_blank')}
                      >
                        <Play className="h-4 w-4 mr-1" />
                        Watch Video
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => 
                          updateNodeMutation.mutate({
                            nodeId: selectedNode.id,
                            isCompleted: !selectedNode.isCompleted
                          })
                        }
                        disabled={updateNodeMutation.isPending}
                      >
                        {selectedNode.isCompleted ? (
                          <>
                            <Circle className="h-4 w-4 mr-1" />
                            Mark Incomplete
                          </>
                        ) : (
                          <>
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Mark Complete
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Learning Path Stats */}
          <div className="grid grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{totalNodes}</div>
                <div className="text-sm text-gray-600">Total Videos</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{completedNodes}</div>
                <div className="text-sm text-gray-600">Completed</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">{totalNodes - completedNodes}</div>
                <div className="text-sm text-gray-600">Remaining</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}