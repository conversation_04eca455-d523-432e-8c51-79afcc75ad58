import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { apiRequest } from "@/lib/queryClient";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Plus, Loader2, Lock, Globe, Users, Shield } from "lucide-react";

const createPlanSchema = z.object({
  title: z
    .string()
    .min(1, "Title is required")
    .max(100, "Title must be less than 100 characters"),
  description: z
    .string()
    .max(500, "Description must be less than 500 characters")
    .optional(),
  isPublic: z.boolean().default(false),
});

type CreatePlanFormData = z.infer<typeof createPlanSchema>;

interface CreatePlanModalProps {
  children: React.ReactNode;
}

export default function CreatePlanModal({ children }: CreatePlanModalProps) {
  const [open, setOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<CreatePlanFormData>({
    resolver: zodResolver(createPlanSchema),
    defaultValues: {
      title: "",
      description: "",
      isPublic: false,
    },
  });

  const createPlanMutation = useMutation({
    mutationFn: async (planData: CreatePlanFormData) => {
      return await apiRequest("/api/learning-plans", {
        method: "POST",
        body: JSON.stringify(planData),
      });
    },
    onSuccess: (newPlan) => {
      queryClient.invalidateQueries({ queryKey: ["/api/learning-plans"] });
      toast({
        title: "Success",
        description: "Learning plan created successfully!",
      });
      setOpen(false);
      form.reset();
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to create learning plan. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: CreatePlanFormData) => {
    createPlanMutation.mutate(data);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Learning Plan</DialogTitle>
          <DialogDescription>
            Create a personalized learning plan to organize your educational
            journey.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Plan Title</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., React Development Fundamentals"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Give your learning plan a clear, descriptive title.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe what you'll learn in this plan..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Briefly describe the goals and topics for this learning
                    plan.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isPublic"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Plan Privacy</FormLabel>
                  <FormControl>
                    <RadioGroup
                      value={field.value ? "public" : "private"}
                      onValueChange={(value) =>
                        field.onChange(value === "public")
                      }
                      className="space-y-4"
                    >
                      <div className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                        <RadioGroupItem
                          value="private"
                          id="private"
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <Label
                            htmlFor="private"
                            className="flex items-center gap-2 font-medium cursor-pointer"
                          >
                            <Lock className="w-4 h-4 text-gray-600" />
                            Private Plan
                          </Label>
                          <p className="text-sm text-gray-600 mt-1">
                            Only you can add videos. You control who can view
                            and share this plan.
                          </p>
                          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                              <Shield className="w-3 h-3" />
                              Full control
                            </span>
                            <span className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              Selective sharing
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                        <RadioGroupItem
                          value="public"
                          id="public"
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <Label
                            htmlFor="public"
                            className="flex items-center gap-2 font-medium cursor-pointer"
                          >
                            <Globe className="w-4 h-4 text-green-600" />
                            Public Plan
                          </Label>
                          <p className="text-sm text-gray-600 mt-1">
                            Anyone can view, add videos, and share this plan
                            with others.
                          </p>
                          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                              <Globe className="w-3 h-3" />
                              Open access
                            </span>
                            <span className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              Community driven
                            </span>
                          </div>
                        </div>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormDescription>
                    Choose who can access and contribute to your learning plan.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={createPlanMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createPlanMutation.isPending}
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                {createPlanMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Plan
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
