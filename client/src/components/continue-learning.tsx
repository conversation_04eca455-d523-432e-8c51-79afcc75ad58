import { useQuery, useQ<PERSON>y<PERSON>lient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { Link } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Play, Clock } from "lucide-react";
import VideoCard from "@/components/video-card";

interface VideoWithProgress {
  id: number;
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  duration: string;
  channelTitle: string;
  progress?: {
    currentTime: number;
    isCompleted: boolean;
    progressPercentage: number;
  };
  category?: string;
}

export default function ContinueLearning() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const { data: continueVideos = [], isLoading, refetch } = useQuery({
    queryKey: ["/api/continue-learning"],
    enabled: !!user,
    staleTime: 0, // Always consider data stale for real-time updates
    cacheTime: 30 * 1000, // 30 seconds cache
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
    onSuccess: (data) => {
      console.log('📺 Continue learning data updated:', data);
    },
    onError: (error) => {
      console.error('❌ Continue learning error:', error);
    }
  });

  if (isLoading) {
    return (
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-slate-800">Last Played Video</h3>
        </div>
        <div className="grid gap-6">
          {[...Array(2)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <div className="w-full h-32 bg-slate-200 rounded-t-xl"></div>
              <CardContent className="p-4">
                <div className="space-y-2">
                  <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                  <div className="h-3 bg-slate-200 rounded w-1/2"></div>
                  <div className="h-2 bg-slate-200 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (continueVideos.length === 0) {
    return (
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-slate-800">Last Played Video</h3>
        </div>
        <Card>
          <CardContent className="py-12 text-center">
            <div className="text-slate-400 mb-4">
              <Play className="w-12 h-12 mx-auto" />
            </div>
            <h4 className="text-lg font-medium text-slate-800 mb-2">No videos watched yet</h4>
            <p className="text-slate-600 mb-4">
              Start watching videos to see your last played video here.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-slate-800">Last Played Video</h3>
        <div className="flex items-center gap-4">
          <button 
            onClick={() => {
              console.log('🔄 Manual refresh triggered');
              queryClient.invalidateQueries({ queryKey: ["/api/continue-learning"] });
              refetch();
            }}
            className="text-blue-500 text-sm font-medium hover:text-blue-600 transition-colors"
          >
            Refresh
          </button>
          <Link href="/progress">
            <a className="text-blue-500 text-sm font-medium hover:text-blue-600 transition-colors">
              View All
            </a>
          </Link>
        </div>
      </div>

      <div className="grid gap-6">
        {continueVideos.slice(0, 1).map((video: VideoWithProgress) => (
          <VideoCard
            key={video.id}
            video={video}
            progress={video.progress}
            category={video.category}
            showProgress={true}
            showFavoriteButton={true}
          />
        ))}
      </div>
    </div>
  );
}
