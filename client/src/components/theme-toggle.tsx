import { Moon, Sun, Palette } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useTheme } from '@/contexts/ThemeContext';

const themes = [
  { value: 'light', label: 'Light', icon: Sun },
  { value: 'dark', label: 'Dark', icon: Moon },
  { value: 'modern-blue', label: 'Blue', color: 'bg-blue-500' },
  { value: 'modern-purple', label: 'Purple', color: 'bg-purple-500' },
  { value: 'modern-green', label: 'Green', color: 'bg-green-500' },
  { value: 'modern-orange', label: 'Orange', color: 'bg-orange-500' },
] as const;

export function ThemeToggle() {
  const { theme, isDark, setTheme, toggleDark } = useTheme();
  const currentTheme = themes.find(t => t.value === theme);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="w-9 h-9 p-0"
          title="Change theme"
        >
          {theme === 'dark' || (isDark && theme === 'light') ? (
            <Moon className="h-4 w-4" />
          ) : theme === 'light' ? (
            <Sun className="h-4 w-4" />
          ) : (
            <Palette className="h-4 w-4" />
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {themes.map((themeOption) => {
          const Icon = themeOption.icon;
          const isActive = theme === themeOption.value;
          
          return (
            <DropdownMenuItem
              key={themeOption.value}
              onClick={() => setTheme(themeOption.value)}
              className="flex items-center gap-2"
            >
              {Icon ? (
                <Icon className="h-4 w-4" />
              ) : (
                <div className={`w-4 h-4 rounded-full ${themeOption.color}`} />
              )}
              <span>{themeOption.label}</span>
              {isActive && (
                <div className="ml-auto w-2 h-2 bg-current rounded-full" />
              )}
            </DropdownMenuItem>
          );
        })}
        
        {theme !== 'light' && theme !== 'dark' && (
          <>
            <div className="border-t my-1" />
            <DropdownMenuItem
              onClick={toggleDark}
              className="flex items-center gap-2"
            >
              {isDark ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
              <span>{isDark ? 'Light Mode' : 'Dark Mode'}</span>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}