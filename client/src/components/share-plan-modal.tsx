import { useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Share2, 
  Mail, 
  Users, 
  Copy, 
  Check,
  UserPlus
} from "lucide-react";

interface Friend {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  profileImageUrl: string;
}

interface SharePlanModalProps {
  children: React.ReactNode;
  planId: number;
  planTitle: string;
}

export default function SharePlanModal({ children, planId, planTitle }: SharePlanModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [selectedFriend, setSelectedFriend] = useState<string | null>(null);
  const [shareUrl, setShareUrl] = useState("");
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  // Fetch friends list
  const { data: friends = [] } = useQuery<Friend[]>({
    queryKey: ["/api/friends"],
    enabled: isOpen,
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "Please log in again",
          variant: "destructive",
        });
      }
    },
  });

  // Share plan mutation
  const sharePlanMutation = useMutation({
    mutationFn: async (data: { email?: string; friendId?: string }) => {
      return await apiRequest(`/api/learning-plans/${planId}/share`, {
        method: "POST",
        body: JSON.stringify(data)
      });
    },
    onSuccess: (data) => {
      if (data.shareUrl) {
        setShareUrl(data.shareUrl);
      }
      
      toast({
        title: data.userExists ? "Plan Shared!" : "Share Link Generated",
        description: data.message,
        variant: "default",
      });
      
      if (data.userExists) {
        setEmail("");
        setSelectedFriend(null);
      }
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to share plan",
        variant: "destructive",
      });
    },
  });

  const handleEmailShare = () => {
    if (email.trim()) {
      sharePlanMutation.mutate({ email: email.trim() });
    }
  };

  const handleFriendShare = (friendId: string) => {
    setSelectedFriend(friendId);
    sharePlanMutation.mutate({ friendId });
  };

  const handleGenerateLink = () => {
    sharePlanMutation.mutate({});
  };

  const copyToClipboard = async () => {
    if (shareUrl) {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      toast({
        title: "Copied!",
        description: "Share link copied to clipboard",
      });
    }
  };

  const getDisplayName = (friend: Friend) => {
    if (friend.firstName && friend.lastName) {
      return `${friend.firstName} ${friend.lastName}`;
    }
    return friend.email;
  };

  const getInitials = (friend: Friend) => {
    return `${friend.firstName?.[0] || ""}${friend.lastName?.[0] || ""}`.toUpperCase() || "?";
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="w-5 h-5" />
            Share "{planTitle}"
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="email" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="email">
              <Mail className="w-4 h-4 mr-1" />
              Email
            </TabsTrigger>
            <TabsTrigger value="friends">
              <Users className="w-4 h-4 mr-1" />
              Friends
            </TabsTrigger>
            <TabsTrigger value="link">
              <Copy className="w-4 h-4 mr-1" />
              Link
            </TabsTrigger>
          </TabsList>

          <TabsContent value="email" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter recipient's email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <Button 
              onClick={handleEmailShare}
              disabled={!email.trim() || sharePlanMutation.isPending}
              className="w-full"
            >
              <Mail className="w-4 h-4 mr-2" />
              {sharePlanMutation.isPending ? "Sharing..." : "Share Plan"}
            </Button>
            <p className="text-xs text-gray-500">
              If they have an account, the plan will be added directly. 
              Otherwise, they'll get a signup link.
            </p>
          </TabsContent>

          <TabsContent value="friends" className="space-y-4">
            {friends.length === 0 ? (
              <div className="text-center py-6">
                <UserPlus className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600 mb-2">No friends yet</p>
                <p className="text-xs text-gray-500">
                  Add friends to share plans directly with them
                </p>
              </div>
            ) : (
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {friends.map((friend) => (
                  <div
                    key={friend.id}
                    className={`flex items-center justify-between p-2 rounded-lg border cursor-pointer transition-colors ${
                      selectedFriend === friend.id 
                        ? "bg-blue-50 border-blue-200" 
                        : "hover:bg-gray-50"
                    }`}
                    onClick={() => handleFriendShare(friend.id)}
                  >
                    <div className="flex items-center gap-3">
                      <Avatar className="w-8 h-8">
                        <AvatarImage src={friend.profileImageUrl} />
                        <AvatarFallback className="text-xs">
                          {getInitials(friend)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-sm">
                          {getDisplayName(friend)}
                        </p>
                        <p className="text-xs text-gray-500">{friend.email}</p>
                      </div>
                    </div>
                    {sharePlanMutation.isPending && selectedFriend === friend.id ? (
                      <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Share2 className="w-4 h-4 text-gray-400" />
                    )}
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="link" className="space-y-4">
            <Button 
              onClick={handleGenerateLink}
              disabled={sharePlanMutation.isPending}
              className="w-full"
            >
              <Share2 className="w-4 h-4 mr-2" />
              {sharePlanMutation.isPending ? "Generating..." : "Generate Share Link"}
            </Button>
            
            {shareUrl && (
              <div className="space-y-2">
                <Label>Share Link</Label>
                <div className="flex gap-2">
                  <Input
                    value={shareUrl}
                    readOnly
                    className="text-xs"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={copyToClipboard}
                  >
                    {copied ? (
                      <Check className="w-4 h-4" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </Button>
                </div>
                <p className="text-xs text-gray-500">
                  Anyone with this link can view and copy the plan
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}