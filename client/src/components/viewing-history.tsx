import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { Link } from "wouter";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { History, Clock, Play, Eye } from "lucide-react";

export default function ViewingHistory() {
  const { isAuthenticated } = useAuth();
  
  const { data: viewingHistory = [], isLoading } = useQuery({
    queryKey: ['/api/user/viewing-history'],
    enabled: isAuthenticated,
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="w-5 h-5" />
            Recent Viewing History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex space-x-3 animate-pulse">
                <div className="w-20 h-12 bg-slate-200 rounded"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                  <div className="h-3 bg-slate-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (viewingHistory.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="w-5 h-5" />
            Recent Viewing History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <History className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600 mb-4">No viewing history yet</p>
            <Link href="/explore">
              <Button size="sm">
                <Play className="w-4 h-4 mr-2" />
                Start Watching
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <History className="w-5 h-5" />
            Recent Viewing History
            <Badge variant="secondary" className="text-xs">
              Last 6
            </Badge>
          </CardTitle>
          <Link href="/profile">
            <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
              View All
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
          {viewingHistory.slice(0, 6).map((item: any) => (
            <Link key={item.id} href={`/video/${item.video.youtubeId}`}>
              <div className="group cursor-pointer">
                <div className="relative">
                  <img
                    src={item.video.thumbnailUrl}
                    alt={item.video.title}
                    className="w-full h-16 sm:h-20 object-cover rounded-lg group-hover:opacity-90 transition-opacity"
                  />
                  {item.isCompleted ? (
                    <div className="absolute top-1 right-1 bg-green-500 text-white rounded-full p-1">
                      <Play className="w-2 h-2 fill-current" />
                    </div>
                  ) : item.currentTime > 0 && (
                    <div className="absolute bottom-1 left-1 right-1">
                      <div className="w-full bg-black/50 rounded-full h-1">
                        <div 
                          className="bg-blue-500 h-1 rounded-full"
                          style={{ width: `${Math.min((item.currentTime / 100) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                  <div className="absolute bottom-1 right-1 bg-black/75 text-white text-xs px-1 py-0.5 rounded">
                    {item.video.duration || 'N/A'}
                  </div>
                </div>
                <div className="mt-2">
                  <h4 className="text-xs sm:text-sm font-medium line-clamp-2 group-hover:text-blue-600 transition-colors">
                    {item.video.title}
                  </h4>
                  <p className="text-xs text-slate-500 mt-1 line-clamp-1">
                    {item.video.channelTitle}
                  </p>
                  <div className="flex items-center gap-1 mt-1 text-xs text-slate-400">
                    <Clock className="w-3 h-3" />
                    <span className="hidden sm:inline">{new Date(item.lastWatched).toLocaleDateString()}</span>
                    <span className="sm:hidden">{new Date(item.lastWatched).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}