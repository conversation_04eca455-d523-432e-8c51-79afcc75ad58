import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface CaptchaProps {
  onVerify: (isValid: boolean) => void;
  reset?: boolean;
}

export function Captcha({ onVerify, reset }: CaptchaProps) {
  const [num1, setNum1] = useState(0);
  const [num2, setNum2] = useState(0);
  const [userAnswer, setUserAnswer] = useState('');
  const [isValid, setIsValid] = useState(false);

  const generateCaptcha = () => {
    const n1 = Math.floor(Math.random() * 10) + 1;
    const n2 = Math.floor(Math.random() * 10) + 1;
    setNum1(n1);
    setNum2(n2);
    setUserAnswer('');
    setIsValid(false);
    onVerify(false);
  };

  useEffect(() => {
    generateCaptcha();
  }, [reset]);

  useEffect(() => {
    const correctAnswer = num1 + num2;
    const valid = parseInt(userAnswer) === correctAnswer;
    setIsValid(valid);
    onVerify(valid);
  }, [userAnswer, num1, num2, onVerify]);

  return (
    <div className="space-y-2">
      <Label htmlFor="captcha">Security Check</Label>
      <div className="flex items-center gap-2">
        <div className="bg-slate-100 dark:bg-slate-800 px-3 py-2 rounded border text-center min-w-[80px]">
          {num1} + {num2} = ?
        </div>
        <Input
          id="captcha"
          type="number"
          placeholder="Answer"
          value={userAnswer}
          onChange={(e) => setUserAnswer(e.target.value)}
          className="w-20"
        />
        <button
          type="button"
          onClick={generateCaptcha}
          className="text-blue-500 hover:text-blue-700 text-sm"
        >
          Refresh
        </button>
      </div>
      {userAnswer && !isValid && (
        <p className="text-red-500 text-sm">Incorrect answer</p>
      )}
    </div>
  );
}