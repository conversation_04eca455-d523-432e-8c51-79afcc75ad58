import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Play, Clock, CheckCircle, PlayCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface PlaylistSidebarProps {
  planSlug?: string | null;
  currentVideoId: number;
  onVideoSelect: (videoId: number) => void;
  isMobile?: boolean;
  autoPlayEnabled?: boolean;
  onAutoPlayChange?: (enabled: boolean) => void;
}

interface PlanVideo {
  id: number;
  videoId: number;
  orderIndex: number;
  video: {
    id: number;
    youtubeId: string;
    title: string;
    description: string;
    thumbnailUrl: string;
    duration: string;
    channelTitle: string;
  };
}

interface VideoProgress {
  videoId: number;
  currentTime: number;
  isCompleted: boolean;
}

export default function PlaylistSidebar({ 
  planSlug, 
  currentVideoId, 
  onVideoSelect, 
  isMobile = false,
  autoPlayEnabled = false,
  onAutoPlayChange
}: PlaylistSidebarProps) {
  const [localAutoPlay, setLocalAutoPlay] = useState(autoPlayEnabled);
  const queryClient = useQueryClient();
  
  // Get plan details and videos
  const { data: planData } = useQuery({
    queryKey: [`/api/learning-plans/slug/${planSlug || 'none'}`],
    enabled: !!planSlug,
  });

  const { data: planVideos = [] } = useQuery<PlanVideo[]>({
    queryKey: [`/api/learning-plans/${planData?.id || 0}/videos`],
    enabled: !!planData?.id,
  });

  const { data: progressData = [] } = useQuery<VideoProgress[]>({
    queryKey: [`/api/progress/plan/${planData?.id || 0}`],
    enabled: !!planData?.id,
    staleTime: 0,
    cacheTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  // Create progress map for quick lookup
  const progressMap = progressData.reduce((acc, progress) => {
    acc[progress.videoId] = progress;
    return acc;
  }, {} as Record<number, VideoProgress>);

  const formatDuration = (duration: string) => {
    if (!duration) return "0:00";
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return "0:00";
    
    const hours = parseInt(match[1]) || 0;
    const minutes = parseInt(match[2]) || 0;
    const seconds = parseInt(match[3]) || 0;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = (videoId: number) => {
    const progress = progressMap[videoId];
    console.log(`Progress for video ${videoId}:`, progress); // Debug log
    if (!progress) return 0;
    if (progress.isCompleted) {
      console.log(`Video ${videoId} is completed, returning 100%`);
      return 100;
    }
    // For incomplete videos, show actual progress based on current time
    if (progress.currentTime > 0) {
      const percentage = Math.max(5, Math.min((progress.currentTime / 600) * 100, 95));
      console.log(`Video ${videoId} progress: ${percentage}%`);
      return percentage;
    }
    return 0;
  };

  // Always render the same component structure to maintain hook count
  const hasValidPlan = planSlug && planData && planVideos.length > 0;
  
  const currentIndex = hasValidPlan ? planVideos.findIndex(pv => pv.video.id === currentVideoId) : -1;
  const completedCount = hasValidPlan ? planVideos.filter(pv => progressMap[pv.video.id]?.isCompleted).length : 0;
  
  console.log(`Playlist sidebar for "${planData?.title}":`, {
    hasValidPlan,
    totalVideos: planVideos.length,
    completedCount,
    progressDataLength: progressData.length,
    progressMapKeys: Object.keys(progressMap).length
  });
  
  // Listen for video completion events to refresh progress
  useEffect(() => {
    const handleVideoCompleted = () => {
      console.log('🎉 Video completed, refreshing playlist sidebar progress');
      queryClient.invalidateQueries({ 
        queryKey: [`/api/progress/plan/${planData?.id || 0}`],
        refetchType: 'active'
      });
    };
    
    window.addEventListener('videoCompleted', handleVideoCompleted);
    
    return () => {
      window.removeEventListener('videoCompleted', handleVideoCompleted);
    };
  }, [planData?.id, queryClient]);
  
  // Get next video for auto-play
  const nextVideo = hasValidPlan && currentIndex >= 0 && currentIndex < planVideos.length - 1 
    ? planVideos[currentIndex + 1] 
    : null;
  
  // Expose next video info for parent component - always call this hook
  useEffect(() => {
    (window as any).nextVideoId = nextVideo?.video.id || null;
  }, [nextVideo?.video.id]);
  
  if (!hasValidPlan) {
    return (
      <Card className={cn("h-fit", isMobile && "mt-4")}>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">No Playlist</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-slate-600">
            This video is not part of a learning plan.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("h-fit", isMobile && "mt-4")}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center justify-between">
          <span className="truncate">{planData.title}</span>
          <span className="text-sm font-normal text-slate-500">
            {currentIndex + 1}/{planVideos.length}
          </span>
        </CardTitle>
        <div className="flex items-center gap-2 text-sm text-slate-600">
          <span>{completedCount}/{planVideos.length} completed</span>
          <Progress 
            value={planVideos.length > 0 ? Math.round((completedCount / planVideos.length) * 100) : 0} 
            className="flex-1 h-2" 
          />
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className={cn(
          "space-y-1",
          isMobile ? "max-h-64 overflow-y-auto" : "max-h-96 overflow-y-auto"
        )}>
          {planVideos.map((planVideo, index) => {
            const isCurrentVideo = planVideo.video.id === currentVideoId;
            const progress = progressMap[planVideo.video.id];
            const isCompleted = progress?.isCompleted || false;
            const progressPercentage = getProgressPercentage(planVideo.video.id);
            
            return (
              <div
                key={planVideo.id}
                className={cn(
                  "flex gap-3 p-3 cursor-pointer hover:bg-slate-50 transition-colors border-l-4",
                  isCurrentVideo 
                    ? "bg-blue-50 border-l-blue-500" 
                    : "border-l-transparent hover:border-l-slate-200"
                )}
                onClick={() => !isCurrentVideo && onVideoSelect(planVideo.video.id)}
              >
                {/* Thumbnail */}
                <div className="relative flex-shrink-0">
                  <img
                    src={planVideo.video.thumbnailUrl}
                    alt={planVideo.video.title}
                    className="w-20 h-12 object-cover rounded"
                  />
                  
                  {/* Play indicator */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    {isCurrentVideo ? (
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <PlayCircle className="w-4 h-4 text-white" />
                      </div>
                    ) : isCompleted ? (
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-white" />
                      </div>
                    ) : (
                      <div className="w-6 h-6 bg-black bg-opacity-70 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <Play className="w-3 h-3 text-white ml-0.5" />
                      </div>
                    )}
                  </div>
                  
                  {/* Duration */}
                  <div className="absolute bottom-1 right-1 bg-black bg-opacity-80 text-white text-xs px-1 rounded">
                    {formatDuration(planVideo.video.duration)}
                  </div>
                  
                  {/* Progress bar */}
                  {progressPercentage > 0 && (
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-black bg-opacity-30">
                      <div 
                        className={`h-full ${isCompleted ? 'bg-green-500' : 'bg-red-500'}`}
                        style={{ width: `${progressPercentage}%` }}
                      />
                    </div>
                  )}
                </div>
                
                {/* Video Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start gap-2">
                    <span className="text-sm font-medium text-slate-400 flex-shrink-0">
                      {index + 1}.
                    </span>
                    <div className="min-w-0">
                      <h4 className={cn(
                        "text-sm font-medium line-clamp-2 leading-tight",
                        isCurrentVideo ? "text-blue-600" : "text-slate-800"
                      )}>
                        {planVideo.video.title}
                      </h4>
                      <p className="text-xs text-slate-500 mt-1">
                        {planVideo.video.channelTitle}
                      </p>
                      
                      {/* Progress info */}
                      {progress && !isCompleted && (
                        <div className="flex items-center gap-1 mt-1">
                          <Clock className="w-3 h-3 text-slate-400" />
                          <span className="text-xs text-slate-500">
                            {Math.floor(progress.currentTime / 60)}:{(progress.currentTime % 60).toString().padStart(2, '0')}
                          </span>
                        </div>
                      )}
                      
                      {isCompleted && (
                        <div className="flex items-center gap-1 mt-1">
                          <CheckCircle className="w-3 h-3 text-green-500" />
                          <span className="text-xs text-green-600">Completed</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        {/* Auto-play next toggle */}
        <div className="p-3 border-t bg-slate-50">
          <div className="flex items-center justify-between text-sm">
            <span className="text-slate-600">Auto-play next video</span>
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localAutoPlay}
                onChange={(e) => {
                  setLocalAutoPlay(e.target.checked);
                  onAutoPlayChange?.(e.target.checked);
                }}
                className="sr-only"
              />
              <div className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                localAutoPlay ? 'bg-blue-500' : 'bg-gray-300'
              }`}>
                <div className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                  localAutoPlay ? 'translate-x-5' : 'translate-x-1'
                }`} />
              </div>
            </label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}