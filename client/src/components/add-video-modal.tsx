import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { apiRequest } from "@/lib/queryClient";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, Plus, Clock, Eye, CheckCircle } from "lucide-react";

interface YouTubeVideo {
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  channelTitle: string;
  publishedAt: string;
  duration?: string;
  viewCount?: number;
}

interface AddVideoModalProps {
  children: React.ReactNode;
  planId: number;
}

export default function AddVideoModal({ children, planId }: AddVideoModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<YouTubeVideo[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [addedVideos, setAddedVideos] = useState<Set<string>>(new Set());
  const [loadingVideos, setLoadingVideos] = useState<Set<string>>(new Set());
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const searchMutation = useMutation({
    mutationFn: async (query: string) => {
      const url = `/api/youtube/search?q=${encodeURIComponent(query)}&maxResults=6`;
      const data = await apiRequest(url, { method: "GET" });
      return data;
    },
    onSuccess: (data) => {
      console.log('Search successful, results:', data);
      // Handle both direct array and object with videos property
      const videos = data.videos || data;
      setSearchResults(Array.isArray(videos) ? videos : []);
      setIsSearching(false);
    },
    onError: (error) => {
      console.error('Search error:', error);
      
      setIsSearching(false);
      setSearchResults([]);
      
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      
      let errorMessage = error?.response?.data?.message || error?.message || "Failed to search videos";
      
      // Handle quota exceeded error with user-friendly message
      if (errorMessage.includes('quota') || errorMessage.includes('quotaExceeded')) {
        errorMessage = "YouTube search temporarily unavailable due to high usage. Please try again later or tomorrow.";
      }
      
      toast({
        title: "Search Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const addVideoMutation = useMutation({
    mutationFn: async (video: YouTubeVideo) => {
      // Set loading state for this specific video
      setLoadingVideos(prev => new Set([...prev, video.youtubeId]));
      
      try {
        // First create the video
        const createdVideo = await apiRequest("/api/videos", {
          method: "POST",
          body: JSON.stringify(video)
        });
        
        // Get the current number of videos in the plan for ordering
        const planVideos = await apiRequest(`/api/learning-plans/${planId}/videos`, { method: "GET" });
        
        // Add video to the plan
        const addToPlanResponse = await apiRequest(`/api/learning-plans/${planId}/videos`, {
          method: "POST",
          body: JSON.stringify({
            videoId: createdVideo.id,
            orderIndex: planVideos.length
          })
        });
        
        return { ...createdVideo, youtubeId: video.youtubeId };
      } finally {
        // Remove loading state for this video
        setLoadingVideos(prev => {
          const newSet = new Set(prev);
          newSet.delete(video.youtubeId);
          return newSet;
        });
      }
    },
    onSuccess: (data, variables) => {
      // Mark video as added
      setAddedVideos(prev => new Set([...prev, variables.youtubeId]));
      
      toast({
        title: "Success",
        description: "Video added to learning plan",
      });
      queryClient.invalidateQueries({ queryKey: [`/api/learning-plans/${planId}/videos`] });
      queryClient.invalidateQueries({ queryKey: ["/api/learning-plans"] });
      // Keep modal open for adding multiple videos
      // User can close manually when done
    },
    onError: (error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to add video. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSearch = () => {
    if (searchQuery.trim()) {
      console.log('Starting search for:', searchQuery.trim());
      setIsSearching(true);
      setSearchResults([]); // Clear previous results
      searchMutation.mutate(searchQuery.trim());
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const formatDuration = (duration?: string) => {
    if (!duration) return "";
    
    // Parse ISO 8601 duration (PT4M13S)
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return duration;
    
    const hours = parseInt(match[1]) || 0;
    const minutes = parseInt(match[2]) || 0;
    const seconds = parseInt(match[3]) || 0;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatViewCount = (viewCount?: number) => {
    if (!viewCount) return "";
    
    if (viewCount >= 1000000) {
      return `${(viewCount / 1000000).toFixed(1)}M views`;
    } else if (viewCount >= 1000) {
      return `${(viewCount / 1000).toFixed(1)}K views`;
    }
    return `${viewCount} views`;
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      // Reset state when modal is closed
      setSearchQuery("");
      setSearchResults([]);
      setAddedVideos(new Set());
      setLoadingVideos(new Set());
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-slate-800">
            Add Video to Learning Plan
          </DialogTitle>
          <DialogDescription>
            Search and add YouTube videos to enhance your learning journey.
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden flex flex-col">
          {/* Search Bar */}
          <div className="relative mb-6">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="w-5 h-5 text-slate-400" />
            </div>
            <Input
              type="text"
              placeholder="Search YouTube videos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="pl-10 h-12"
            />
            {isSearching && (
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
          </div>

          <Button 
            onClick={handleSearch}
            disabled={!searchQuery.trim() || isSearching}
            className="mb-6 bg-blue-500 hover:bg-blue-600 text-white"
          >
            <Search className="w-4 h-4 mr-2" />
            Search Videos
          </Button>

          {/* Loading State */}
          {isSearching && (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-slate-600">Searching videos...</p>
              </div>
            </div>
          )}

          {/* Search Results */}
          {!isSearching && searchResults.length > 0 && (
            <div className="flex-1 overflow-y-auto">
              <div className="grid gap-4">
                {searchResults.map((video) => (
                  <div
                    key={video.youtubeId}
                    className="flex space-x-4 p-4 border border-slate-200 rounded-lg hover:shadow-md transition-shadow"
                  >
                    <img
                      src={video.thumbnailUrl || '/api/placeholder/160/90'}
                      alt={video.title}
                      className="w-40 h-24 object-cover rounded-lg flex-shrink-0"
                    />
                    
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-slate-800 mb-2 line-clamp-2">
                        {video.title}
                      </h4>
                      
                      <p className="text-sm text-slate-600 mb-3 line-clamp-2">
                        {video.description}
                      </p>
                      
                      <div className="flex items-center space-x-4 mb-3">
                        <span className="text-sm text-slate-500">{video.channelTitle}</span>
                        {video.duration && (
                          <Badge variant="secondary" className="text-xs">
                            <Clock className="w-3 h-3 mr-1" />
                            {formatDuration(video.duration)}
                          </Badge>
                        )}
                        {video.viewCount && (
                          <Badge variant="outline" className="text-xs">
                            <Eye className="w-3 h-3 mr-1" />
                            {formatViewCount(video.viewCount)}
                          </Badge>
                        )}
                      </div>
                      
                      <Button
                        size="sm"
                        onClick={() => addVideoMutation.mutate(video)}
                        disabled={loadingVideos.has(video.youtubeId) || addedVideos.has(video.youtubeId)}
                        className={`${
                          addedVideos.has(video.youtubeId)
                            ? "bg-green-500 hover:bg-green-600 text-white"
                            : "bg-blue-500 hover:bg-blue-600 text-white"
                        }`}
                      >
                        {addedVideos.has(video.youtubeId) ? (
                          <>
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Added
                          </>
                        ) : loadingVideos.has(video.youtubeId) ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                            Adding...
                          </>
                        ) : (
                          <>
                            <Plus className="w-4 h-4 mr-2" />
                            Add to Plan
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {!isSearching && searchResults.length === 0 && searchQuery && (
            <div className="text-center py-8 text-slate-500">
              <Search className="w-12 h-12 mx-auto mb-4 text-slate-300" />
              <p>No videos found. Try a different search term.</p>
            </div>
          )}

          {!isSearching && !searchQuery && (
            <div className="text-center py-8 text-slate-400">
              <Search className="w-12 h-12 mx-auto mb-4" />
              <p>Enter a search term to find videos</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}