import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Plus } from "lucide-react";
import CreatePlanModal from "@/components/create-plan-modal";

export default function WelcomeSection() {
  const { user } = useAuth();
  
  const { data: dailyStats } = useQuery<{
    videosWatched: number;
    timeSpent: number;
    streakDays: number;
  }>({
    queryKey: ["/api/analytics/daily"],
    enabled: !!user,
  });

  return (
    <Card className="shadow-sm border border-slate-200">
      <CardContent className="p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-slate-800 mb-2">
              Welcome back, {user?.firstName || 'there'}! 👋
            </h2>
            <p className="text-slate-600">Continue your learning journey and reach your goals</p>
          </div>
          <CreatePlanModal>
            <Button className="mt-4 sm:mt-0 bg-blue-500 hover:bg-blue-600 text-white">
              <Plus className="w-4 h-4 mr-2" />
              New Learning Plan
            </Button>
          </CreatePlanModal>
        </div>

        {/* Quick Stats */}
        <div className="grid sm:grid-cols-3 gap-4">
          <div className="bg-blue-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-500 mb-1">
              {dailyStats?.videosWatched || 0}
            </div>
            <div className="text-sm text-slate-600">Videos Today</div>
          </div>
          <div className="bg-violet-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-violet-500 mb-1">
              {dailyStats?.timeSpent || 0}h
            </div>
            <div className="text-sm text-slate-600">Time Spent</div>
          </div>
          <div className="bg-emerald-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-emerald-500 mb-1">
              {dailyStats?.streakDays || 0}
            </div>
            <div className="text-sm text-slate-600">Day Streak</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
