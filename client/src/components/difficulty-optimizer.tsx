import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Brain, 
  TrendingUp, 
  Target, 
  AlertCircle, 
  CheckCircle, 
  BarChart3,
  Lightbulb,
  Zap,
  Star,
  ThumbsUp,
  ThumbsDown,
  BookOpen,
  Clock,
  Trophy
} from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

interface SkillProfile {
  id: number;
  subject: string;
  skillLevel: number;
  confidence: number;
  completionRate: number;
  totalWatchTime: number;
  strugglingTopics: string[];
  masteredTopics: string[];
  lastAssessed: string;
}

interface AdaptiveInsights {
  overallProgress: {
    averageSkillLevel: number;
    overallCompletionRate: number;
    totalSubjects: number;
    recentAdaptations: number;
  };
  learningStyle: string;
  strugglingAreas: string[];
  strengths: string[];
  nextSteps: string[];
  adaptationTrends: {
    trend: string;
    recentDifficulty: number;
    improvementRate: number;
  };
}

interface DifficultyAnalysis {
  id: number;
  subject: string;
  difficultyLevel: number;
  prerequisites: string[];
  learningObjectives: string[];
  complexityFactors: any;
  estimatedCompletionTime: number;
  cognitiveLoad: number;
  confidence: number;
}

interface OptimizedRecommendation {
  id: number;
  title: string;
  description: string;
  thumbnailUrl: string;
  difficulty: DifficultyAnalysis;
  recommendedOrder: number;
  adaptationReason: string;
}

export default function DifficultyOptimizer() {
  const [selectedSubject, setSelectedSubject] = useState<string>('all');
  const [feedbackVideoId, setFeedbackVideoId] = useState<number | null>(null);
  const queryClient = useQueryClient();

  // Fetch user skill profiles
  const { data: skillProfiles, isLoading: loadingProfiles } = useQuery({
    queryKey: ['/api/user/skill-profile'],
    retry: false,
  });

  // Fetch adaptive learning insights
  const { data: insights, isLoading: loadingInsights } = useQuery({
    queryKey: ['/api/learning/adaptive-insights'],
    retry: false,
  });

  // Fetch struggle analysis
  const { data: struggleAnalysis, isLoading: loadingStruggle } = useQuery({
    queryKey: ['/api/learning/struggle-analysis'],
    retry: false,
  });

  // Record learning adaptation mutation
  const adaptationMutation = useMutation({
    mutationFn: async (data: any) => {
      return await apiRequest('/api/learning/adaptation', {
        method: 'POST',
        body: JSON.stringify(data),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/learning/adaptive-insights'] });
      queryClient.invalidateQueries({ queryKey: ['/api/user/skill-profile'] });
      setFeedbackVideoId(null);
    },
  });

  const handleDifficultyFeedback = (videoId: number, rating: number) => {
    adaptationMutation.mutate({
      videoId,
      adaptationType: 'difficulty_feedback',
      userFeedback: rating,
      wasSkipped: false,
    });
  };

  const getDifficultyColor = (level: number) => {
    if (level <= 3) return 'bg-green-500';
    if (level <= 6) return 'bg-yellow-500';
    if (level <= 8) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getDifficultyLabel = (level: number) => {
    if (level <= 2) return 'Beginner';
    if (level <= 4) return 'Easy';
    if (level <= 6) return 'Medium';
    if (level <= 8) return 'Hard';
    return 'Expert';
  };

  const getSkillLevelColor = (level: number) => {
    if (level <= 3) return 'text-orange-600';
    if (level <= 6) return 'text-yellow-600';
    if (level <= 8) return 'text-blue-600';
    return 'text-purple-600';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'struggling':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Target className="h-4 w-4 text-blue-600" />;
    }
  };

  if (loadingProfiles || loadingInsights || loadingStruggle) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
          <Brain className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            AI Difficulty Optimizer
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Personalized learning path optimization based on your skill level and progress
          </p>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="skills">Skills</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="progression">Progression</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          {insights && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Average Skill Level</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {insights.overallProgress.averageSkillLevel}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Out of 10 skill levels
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {insights.overallProgress.overallCompletionRate}%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Across all subjects
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Subjects</CardTitle>
                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {insights.overallProgress.totalSubjects}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Learning subjects
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Learning Trend</CardTitle>
                  {getTrendIcon(insights.adaptationTrends.trend)}
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold capitalize">
                    {insights.adaptationTrends.trend}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Recent performance
                  </p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Learning Style and Strengths */}
          {insights && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Lightbulb className="h-5 w-5 text-yellow-600" />
                    <span>Learning Style</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-lg font-medium">{insights.learningStyle}</p>
                  <div className="mt-4 space-y-2">
                    <h4 className="font-medium text-sm">Your Strengths:</h4>
                    <div className="flex flex-wrap gap-2">
                      {insights.strengths.map((strength, index) => (
                        <Badge key={index} variant="secondary" className="capitalize">
                          <Star className="h-3 w-3 mr-1" />
                          {strength}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Zap className="h-5 w-5 text-blue-600" />
                    <span>Next Steps</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {insights.nextSteps.map((step, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{step}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Struggling Areas Alert */}
          {insights && insights.strugglingAreas.length > 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Areas needing attention:</strong> {insights.strugglingAreas.join(', ')}
                <br />
                <span className="text-sm text-muted-foreground">
                  Consider reviewing fundamentals or breaking down complex topics.
                </span>
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        {/* Skills Tab */}
        <TabsContent value="skills" className="space-y-4">
          {skillProfiles && skillProfiles.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {skillProfiles.map((profile: SkillProfile) => (
                <Card key={profile.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="capitalize">{profile.subject}</span>
                      <Badge 
                        variant="outline" 
                        className={`${getSkillLevelColor(profile.skillLevel)} border-current`}
                      >
                        Level {profile.skillLevel}
                      </Badge>
                    </CardTitle>
                    <CardDescription>
                      Last assessed: {new Date(profile.lastAssessed).toLocaleDateString()}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Skill Level</span>
                        <span>{profile.skillLevel}/10</span>
                      </div>
                      <Progress value={profile.skillLevel * 10} className="h-2" />
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Confidence</span>
                        <span>{Math.round(profile.confidence * 100)}%</span>
                      </div>
                      <Progress value={profile.confidence * 100} className="h-2" />
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Completion Rate</span>
                        <span>{Math.round(profile.completionRate * 100)}%</span>
                      </div>
                      <Progress value={profile.completionRate * 100} className="h-2" />
                    </div>

                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{Math.round(profile.totalWatchTime / 60)}h watched</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Trophy className="h-3 w-3" />
                        <span>{profile.masteredTopics.length} mastered</span>
                      </div>
                    </div>

                    {profile.strugglingTopics.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium mb-2">Struggling Topics:</h4>
                        <div className="flex flex-wrap gap-1">
                          {profile.strugglingTopics.map((topic, index) => (
                            <Badge key={index} variant="destructive" className="text-xs">
                              {topic}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8">
                <Brain className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Skill Profile Yet</h3>
                <p className="text-muted-foreground text-center">
                  Complete some videos to build your personalized skill profile and get optimized recommendations.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-4">
          {struggleAnalysis && (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                    <span>Learning Velocity Analysis</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {struggleAnalysis.learningVelocity.toFixed(1)}
                      </div>
                      <p className="text-sm text-muted-foreground">Videos per day</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {struggleAnalysis.strugglingSubjects.length}
                      </div>
                      <p className="text-sm text-muted-foreground">Struggling subjects</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {struggleAnalysis.commonIssues.length}
                      </div>
                      <p className="text-sm text-muted-foreground">Common issues</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {struggleAnalysis.commonIssues.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <AlertCircle className="h-5 w-5 text-red-600" />
                      <span>Common Issues</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {struggleAnalysis.commonIssues.map((issue: string, index: number) => (
                        <div key={index} className="flex items-start space-x-2">
                          <AlertCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{issue}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {struggleAnalysis.recommendations.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Lightbulb className="h-5 w-5 text-yellow-600" />
                      <span>Recommendations</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {struggleAnalysis.recommendations.map((rec: string, index: number) => (
                        <div key={index} className="flex items-start space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{rec}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        {/* Progression Tab */}
        <TabsContent value="progression" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <span>Difficulty Progression Path</span>
              </CardTitle>
              <CardDescription>
                Your personalized learning path based on current skill level
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Progression Analysis</h3>
                <p className="text-muted-foreground">
                  Select a subject to see your optimal learning progression path
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Feedback Modal */}
      {feedbackVideoId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Rate Video Difficulty</CardTitle>
              <CardDescription>
                How did you find this video's difficulty level?
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDifficultyFeedback(feedbackVideoId, 1)}
                  className="flex items-center space-x-2"
                >
                  <ThumbsUp className="h-4 w-4" />
                  <span>Too Easy</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDifficultyFeedback(feedbackVideoId, 3)}
                  className="flex items-center space-x-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  <span>Perfect</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDifficultyFeedback(feedbackVideoId, 5)}
                  className="flex items-center space-x-2"
                >
                  <ThumbsDown className="h-4 w-4" />
                  <span>Too Hard</span>
                </Button>
              </div>
              <Button 
                variant="ghost" 
                onClick={() => setFeedbackVideoId(null)}
                className="w-full"
              >
                Cancel
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}