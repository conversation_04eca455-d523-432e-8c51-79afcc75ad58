import { useEffect, useRef, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, ExternalLink, RefreshCw } from 'lucide-react';

interface YouTubePlayerProps {
  youtubeId: string;
  title: string;
  onTimeUpdate?: (currentTime: number) => void;
  onDurationChange?: (duration: number) => void;
  onEnded?: () => void;
  startTime?: number;
  className?: string;
}

export default function YouTubePlayer({
  youtubeId,
  title,
  onTimeUpdate,
  onDurationChange,
  onEnded,
  startTime = 0,
  className = '',
}: YouTubePlayerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const playerRef = useRef<any>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Get actual video duration and track progress
  useEffect(() => {
    let videoDuration = 180; // Default
    
    const getVideoDuration = async () => {
      try {
        const YOUTUBE_API_KEY = process.env.REACT_APP_YOUTUBE_API_KEY || '***************************************';
        const response = await fetch(`https://www.googleapis.com/youtube/v3/videos?part=contentDetails&id=${youtubeId}&key=${YOUTUBE_API_KEY}`);
        const data = await response.json();
        
        if (data.items && data.items[0]) {
          const duration = data.items[0].contentDetails.duration;
          videoDuration = parseDuration(duration);
          if (onDurationChange) {
            onDurationChange(videoDuration);
          }
        }
      } catch (error) {
        console.log('Using default duration');
        if (onDurationChange) {
          onDurationChange(videoDuration);
        }
      }
    };

    getVideoDuration();
    
    // Track progress more accurately
    let currentTime = startTime;
    let progressInterval = 0;
    
    intervalRef.current = setInterval(() => {
      progressInterval += 5; // Track every 5 seconds
      currentTime = startTime + progressInterval;
      
      if (onTimeUpdate) {
        onTimeUpdate(currentTime);
      }
      
      // Auto-complete when reaching 95% of video duration
      if (videoDuration > 0 && currentTime >= videoDuration * 0.95) {
        console.log(`🎯 Video ${((currentTime / videoDuration) * 100).toFixed(1)}% complete, triggering onEnded`);
        if (onEnded) {
          onEnded();
        }
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      }
    }, 5000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [youtubeId, startTime, onTimeUpdate, onDurationChange]);

  // Reload iframe when startTime changes to resume from saved position
  useEffect(() => {
    if (startTime > 0) {
      const iframe = document.querySelector(`iframe[title="${title}"]`) as HTMLIFrameElement;
      if (iframe) {
        const newSrc = `https://www.youtube.com/embed/${youtubeId}?autoplay=0&rel=0&controls=1&fs=1&playsinline=1&start=${Math.floor(startTime)}&modestbranding=1`;
        console.log(`🔄 Reloading iframe with start time: ${startTime}s`);
        iframe.src = newSrc;
      }
    }
  }, [startTime, youtubeId, title]);

  // Parse YouTube duration format (PT1M30S) to seconds
  const parseDuration = (duration: string): number => {
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return 180;
    
    const hours = parseInt(match[1]) || 0;
    const minutes = parseInt(match[2]) || 0;
    const seconds = parseInt(match[3]) || 0;
    
    return hours * 3600 + minutes * 60 + seconds;
  };

  const handleRetry = () => {
    setIsLoading(true);
    setHasError(false);
    setRetryCount(prev => prev + 1);
  };

  const openInYouTube = () => {
    window.open(`https://www.youtube.com/watch?v=${youtubeId}`, '_blank', 'noopener,noreferrer');
  };

  if (hasError) {
    return (
      <div className={`relative bg-black ${className}`}>
        <div className="aspect-video w-full">
          <Card className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <CardContent className="text-center p-8">
              <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Video Unavailable
              </h3>
              <p className="text-gray-600 mb-6">
                This video cannot be embedded. It may be restricted or unavailable.
              </p>
              <div className="flex gap-3 justify-center">
                <Button onClick={handleRetry} variant="outline" size="sm">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Retry
                </Button>
                <Button onClick={openInYouTube} size="sm">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Watch on YouTube
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative bg-black ${className}`}>
      {/* Responsive aspect ratio container */}
      <div className="aspect-video w-full">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 z-10">
            <div className="text-center">
              <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-white text-sm">Loading video...</p>
            </div>
          </div>
        )}
        
        <iframe
          key={`${youtubeId}-${Math.floor(startTime || 0)}`}
          className="w-full h-full"
          src={`https://www.youtube.com/embed/${youtubeId}?autoplay=0&rel=0&controls=1&fs=1&playsinline=1&start=${Math.floor(startTime || 0)}&modestbranding=1`}
          title={title}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
          style={{ 
            border: 'none',
            opacity: isLoading ? 0 : 1,
            transition: 'opacity 0.3s ease-in-out'
          }}
          onLoad={() => setIsLoading(false)}
          onError={() => setHasError(true)}
        />
      </div>
    </div>
  );
}