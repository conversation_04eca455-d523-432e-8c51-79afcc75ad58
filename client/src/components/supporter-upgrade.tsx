import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Heart, X, Star, Zap } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";

interface SupporterUpgradeProps {
  onClose?: () => void;
  compact?: boolean;
}

export default function SupporterUpgrade({ onClose, compact = false }: SupporterUpgradeProps) {
  const { user } = useAuth();
  const [isVisible, setIsVisible] = useState(true);

  // Don't show to supporters
  if (user?.isSupporter || !isVisible) {
    return null;
  }

  const handleClose = () => {
    setIsVisible(false);
    onClose?.();
  };

  const handleUpgrade = () => {
    window.open('/donate', '_blank');
  };

  if (compact) {
    return (
      <Card className="border-red-200 bg-gradient-to-r from-red-50 to-pink-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Heart className="w-5 h-5 text-red-500" fill="currentColor" />
              <div>
                <div className="font-medium text-sm">Remove Ads</div>
                <div className="text-xs text-gray-600">Support Learniify & enjoy ad-free learning</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button size="sm" onClick={handleUpgrade} className="bg-red-500 hover:bg-red-600">
                Upgrade
              </Button>
              <Button size="sm" variant="ghost" onClick={handleClose}>
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-red-200 bg-gradient-to-r from-red-50 to-pink-50 relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={handleClose}
        className="absolute top-2 right-2 h-6 w-6 p-0"
      >
        <X className="w-4 h-4" />
      </Button>
      
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Heart className="w-5 h-5 text-red-500" fill="currentColor" />
          Enjoy Ad-Free Learning
          <Badge variant="secondary" className="ml-2">
            <Star className="w-3 h-3 mr-1" />
            Premium
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          <p className="text-gray-700">
            Support Learniify and unlock an ad-free experience while helping us keep education free for everyone.
          </p>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4 text-green-500" />
              <span>No Ads</span>
            </div>
            <div className="flex items-center gap-2">
              <Heart className="w-4 h-4 text-red-500" />
              <span>Support Mission</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4 text-yellow-500" />
              <span>Supporter Badge</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4 text-blue-500" />
              <span>Priority Support</span>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button onClick={handleUpgrade} className="flex-1 bg-red-500 hover:bg-red-600">
              <Heart className="w-4 h-4 mr-2" fill="currentColor" />
              Become a Supporter
            </Button>
            <Button variant="outline" onClick={handleClose}>
              Maybe Later
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}