import { Link, useLocation } from "wouter";
import { Home, Book, Search, TrendingUp } from "lucide-react";

export default function MobileNavigation() {
  const [location] = useLocation();

  const isActive = (path: string) => location === path;

  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-slate-200 px-4 py-2 z-50">
      <div className="flex justify-around">
        <Link
          href="/"
          className={`flex flex-col items-center py-2 transition-colors ${
            isActive("/") ? "text-blue-500" : "text-slate-400"
          }`}
        >
          <Home className="w-5 h-5 mb-1" />
          <span className="text-xs">Home</span>
        </Link>

        <Link
          href="/plans"
          className={`flex flex-col items-center py-2 transition-colors ${
            isActive("/plans") ? "text-blue-500" : "text-slate-400"
          }`}
        >
          <Book className="w-5 h-5 mb-1" />
          <span className="text-xs">Plans</span>
        </Link>

        <Link
          href="/explore"
          className={`flex flex-col items-center py-2 transition-colors ${
            isActive("/explore") ? "text-blue-500" : "text-slate-400"
          }`}
        >
          <Search className="w-5 h-5 mb-1" />
          <span className="text-xs">Explore</span>
        </Link>

        <Link
          href="/progress"
          className={`flex flex-col items-center py-2 transition-colors ${
            isActive("/progress") ? "text-blue-500" : "text-slate-400"
          }`}
        >
          <TrendingUp className="w-5 h-5 mb-1" />
          <span className="text-xs">Progress</span>
        </Link>
      </div>
    </div>
  );
}
