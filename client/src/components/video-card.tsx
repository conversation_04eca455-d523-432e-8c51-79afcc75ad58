import { <PERSON> } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Play, Clock, User, Heart, Plus } from "lucide-react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useState } from "react";

interface VideoCardProps {
  video: {
    id: number;
    youtubeId: string;
    title: string;
    description: string;
    thumbnailUrl: string;
    duration: string;
    channelTitle: string;
  };
  progress?: {
    currentTime: number;
    isCompleted: boolean;
    progressPercentage: number;
  };
  category?: string;
  showProgress?: boolean;
  showFavoriteButton?: boolean;
  showAddToPlan?: boolean;
  className?: string;
}

export default function VideoCard({ 
  video, 
  progress, 
  category, 
  showProgress = true,
  showFavoriteButton = true,
  showAddToPlan = false,
  className = "" 
}: VideoCardProps) {
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedPlanId, setSelectedPlanId] = useState("");

  // Check if video is favorited
  const { data: favoriteStatus } = useQuery({
    queryKey: [`/api/favorites/videos/${video.id}/status`],
    enabled: isAuthenticated && showFavoriteButton,
  });

  const isFavorited = favoriteStatus?.isFavorited || false;

  // Toggle favorite mutation
  const toggleFavoriteMutation = useMutation({
    mutationFn: async () => {
      if (isFavorited) {
        return await apiRequest(`/api/favorites/videos/${video.id}`, {
          method: "DELETE"
        });
      } else {
        return await apiRequest(`/api/favorites/videos/${video.id}`, {
          method: "POST"
        });
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/favorites/videos/${video.id}/status`] });
      queryClient.invalidateQueries({ queryKey: ['/api/favorites/videos'] });
      toast({
        title: isFavorited ? "Removed from Favorites" : "Added to Favorites",
        description: isFavorited 
          ? "Video removed from your favorites" 
          : "Video added to your favorites",
      });
    },
    onError: (error) => {
      console.error("Error toggling favorite:", error);
      toast({
        title: "Error",
        description: "Failed to update favorites",
        variant: "destructive",
      });
    },
  });

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (isAuthenticated) {
      toggleFavoriteMutation.mutate();
    }
  };

  // Get user plans for Add to Plan functionality
  const { data: userPlans = [] } = useQuery({
    queryKey: ['/api/learning-plans'],
    enabled: isAuthenticated && showAddToPlan,
    retry: false,
  });



  // Add to plan mutation
  const addToPlanMutation = useMutation({
    mutationFn: async (planId: number) => {
      const response = await fetch(`/api/learning-plans/${planId}/videos`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          videoId: video.id,
          orderIndex: 0
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to add video');
      }
      
      return data;
    },
    onSuccess: (data) => {
      const selectedPlan = userPlans.find((p: any) => p.id === parseInt(selectedPlanId));
      
      if (data.code === 'ALREADY_EXISTS') {
        toast({ 
          title: "Already Added", 
          description: `This video is already in "${selectedPlan?.title}"`,
          variant: "default"
        });
      } else {
        toast({ 
          title: "Success", 
          description: `Video added to "${selectedPlan?.title}"`
        });
      }
      
      setIsDialogOpen(false);
      setSelectedPlanId("");
      

    },
    onError: (error) => {
      toast({ 
        title: "Error", 
        description: "Failed to add video to plan", 
        variant: "destructive" 
      });
    },
  });

  const handleAddToPlan = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDialogOpen(true);
  };

  const handleConfirmAdd = () => {
    if (selectedPlanId) {
      addToPlanMutation.mutate(parseInt(selectedPlanId));
    }
  };
  const categoryColors = {
    'Web Development': 'bg-blue-100 text-blue-500',
    'Data Science': 'bg-violet-100 text-violet-500',
    'Programming': 'bg-emerald-100 text-emerald-500',
    default: 'bg-slate-100 text-slate-500',
  };

  const categoryClass = categoryColors[category as keyof typeof categoryColors] || categoryColors.default;

  return (
    <Card className={`border border-slate-200 dark:border-slate-700 dark:bg-slate-800 overflow-hidden hover:shadow-md transition-shadow ${className}`}>
      <Link href={`/video/${video.youtubeId}`}>
        <div className="relative">
          <img 
            src={video.thumbnailUrl || '/api/placeholder/600/300'} 
            alt={video.title}
            className="w-full h-32 object-cover cursor-pointer hover:opacity-90 transition-opacity"
          />
          {progress?.isCompleted && (
            <div className="absolute top-2 right-2 bg-green-500 text-white rounded-full p-1">
              <Play className="w-3 h-3" />
            </div>
          )}
          {showFavoriteButton && isAuthenticated && (
            <button
              onClick={handleToggleFavorite}
              disabled={toggleFavoriteMutation.isPending}
              className={`absolute top-2 left-2 p-1.5 rounded-full transition-all duration-200 ${
                isFavorited 
                  ? 'bg-red-500 text-white hover:bg-red-600' 
                  : 'bg-black/50 text-white hover:bg-black/70'
              }`}
              title={isFavorited ? 'Remove from favorites' : 'Add to favorites'}
            >
              <Heart className={`w-3 h-3 ${isFavorited ? 'fill-current' : ''}`} />
            </button>
          )}
          {progress && !progress.isCompleted && progress.progressPercentage > 0 && (
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2">
              <div className="w-full bg-gray-200 rounded-full h-1">
                <div 
                  className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                  style={{ width: `${progress.progressPercentage}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
      </Link>
      
      <CardContent className="p-4">
        {/* Category and Duration */}
        <div className="flex items-center justify-between mb-2">
          {category && (
            <span className={`text-xs px-2 py-1 rounded-full font-medium ${categoryClass}`}>
              {category}
            </span>
          )}
          <div className="flex items-center text-xs text-slate-500">
            <Clock className="w-3 h-3 mr-1" />
            {video.duration}
          </div>
        </div>
        
        {/* Title */}
        <Link href={`/video/${video.youtubeId}`}>
          <h4 className="font-semibold text-slate-800 dark:text-slate-200 mb-2 line-clamp-2 cursor-pointer hover:text-blue-600 transition-colors">
            {video.title}
          </h4>
        </Link>
        
        {/* Channel */}
        <div className="flex items-center text-sm text-slate-600 dark:text-slate-400 mb-3">
          <User className="w-3 h-3 mr-1" />
          {video.channelTitle}
        </div>
        
        {/* Progress */}
        {showProgress && progress && (
          <div className="mb-3">
            <div className="flex items-center justify-between text-sm mb-2">
              <span className="text-slate-600 dark:text-slate-400">Progress</span>
              <span className="font-medium text-slate-800 dark:text-slate-200">
                {progress.isCompleted ? 'Completed' : `${Math.round(progress.progressPercentage)}%`}
              </span>
            </div>
            <Progress value={progress.progressPercentage} className="h-2" />
          </div>
        )}
        
        {/* Action Buttons */}
        <div className="space-y-2">
          <Link href={`/video/${video.youtubeId}`}>
            <Button size="sm" className="w-full bg-blue-500 hover:bg-blue-600 text-white">
              <Play className="w-4 h-4 mr-2" />
              {progress?.isCompleted ? 'Watch Again' : progress ? 'Continue' : 'Watch'}
            </Button>
          </Link>
          
          {showAddToPlan && isAuthenticated && (
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="w-full"
                  onClick={handleAddToPlan}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add to Plan
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add to Learning Plan</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">
                    Add "{video.title}" to a learning plan
                  </p>
                  <Select value={selectedPlanId} onValueChange={setSelectedPlanId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a learning plan" />
                    </SelectTrigger>
                    <SelectContent>
                      {userPlans.map((plan: any) => (
                        <SelectItem key={plan.id} value={plan.id.toString()}>
                          {plan.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <div className="flex gap-2 justify-end">
                    <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleConfirmAdd}
                      disabled={!selectedPlanId || addToPlanMutation.isPending}
                    >
                      {addToPlanMutation.isPending ? "Adding..." : "Add to Plan"}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
