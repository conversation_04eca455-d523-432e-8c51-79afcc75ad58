import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Search, BarChart } from "lucide-react";
import { <PERSON> } from "wouter";
import CreatePlanModal from "@/components/create-plan-modal";

export default function QuickActions() {



  return (
    <Card className="shadow-sm border border-slate-200">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-slate-800">
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <CreatePlanModal>
            <Button
              variant="ghost"
              className="w-full justify-start h-auto p-3 hover:bg-slate-50"
            >
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0 mr-3">
                <Plus className="w-4 h-4 text-blue-500" />
              </div>
              <span className="font-medium text-slate-800">Create New Learning Plan</span>
            </Button>
          </CreatePlanModal>
          
          <Link href="/library">
            <Button
              variant="ghost"
              className="w-full justify-start h-auto p-3 hover:bg-slate-50"
            >
              <div className="w-8 h-8 bg-violet-100 rounded-lg flex items-center justify-center flex-shrink-0 mr-3">
                <Search className="w-4 h-4 text-violet-500" />
              </div>
              <span className="font-medium text-slate-800">Browse Video Library</span>
            </Button>
          </Link>
          
          <Link href="/profile">
            <Button
              variant="ghost"
              className="w-full justify-start h-auto p-3 hover:bg-slate-50"
            >
              <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0 mr-3">
                <BarChart className="w-4 h-4 text-emerald-500" />
              </div>
              <span className="font-medium text-slate-800">View Detailed Progress</span>
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
