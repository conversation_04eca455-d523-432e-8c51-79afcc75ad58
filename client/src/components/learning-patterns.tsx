import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TrendingUp } from "lucide-react";

interface UserMoodProfile {
  currentMood: string;
  preferredLearningTime: string;
  recentActivity: string[];
  skillLevel: string;
  interests: string[];
  completionRate: number;
  avgWatchTime: number;
}

export default function LearningPatterns() {
  const { isAuthenticated } = useAuth();

  // Get user's mood profile
  const { data: moodProfile } = useQuery<UserMoodProfile>({
    queryKey: ["/api/user/mood-profile"],
    enabled: isAuthenticated,
    retry: false,
  });

  if (!isAuthenticated || !moodProfile) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-green-500" />
          Your Learning Patterns
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {moodProfile.completionRate}%
            </div>
            <div className="text-sm text-blue-800">Completion Rate</div>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {moodProfile.avgWatchTime}m
            </div>
            <div className="text-sm text-green-800">Avg Watch Time</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {moodProfile.interests.length}
            </div>
            <div className="text-sm text-purple-800">Interests</div>
          </div>
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">
              {moodProfile.skillLevel}
            </div>
            <div className="text-sm text-orange-800">Skill Level</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}