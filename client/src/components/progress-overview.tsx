import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useMemo } from "react";

type WeeklyProgress = {
  dailyHours: number[];
  totalHours: number;
  completedVideos: number;
};

export default function ProgressOverview() {
  const { user } = useAuth();
  
  const { data: weeklyProgress, error, isLoading } = useQuery<WeeklyProgress>({
    queryKey: ["/api/analytics/weekly"],
    enabled: !!user,
  });

  // Generate chart data for the week with proper error handling
  const chartData = useMemo(() => {
    const dayAbbreviations = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const dailyHours = weeklyProgress?.dailyHours || [0, 0, 0, 0, 0, 0, 0];
    const maxHours = Math.max(...dailyHours, 1); // Ensure minimum of 1 to avoid division by zero
    
    return dayAbbreviations.map((day, index) => ({
      day,
      hours: dailyHours[index] || 0,
      percentage: dailyHours[index] > 0 ? (dailyHours[index] / maxHours) * 100 : 0,
    }));
  }, [weeklyProgress]);

  const getBarColor = (index: number, hours: number) => {
    if (hours === 0) return 'bg-slate-200';
    return index < 5 ? 'bg-blue-500' : 'bg-slate-300';
  };

  if (error) {
    return (
      <Card className="shadow-sm border border-slate-200">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-slate-800">
            This Week's Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-slate-500 py-8">
            Unable to load progress data
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm border border-slate-200">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-slate-800">
          This Week's Progress
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Weekly Chart */}
        <div className="mb-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-slate-500">Loading progress...</div>
            </div>
          ) : (
            <div className="flex items-end justify-between h-32 space-x-2">
              {chartData.map((data, index) => (
                <div key={data.day} className="flex flex-col items-center space-y-2 flex-1">
                  <div 
                    className={`w-full rounded-t transition-all duration-500 ${getBarColor(index, data.hours)}`}
                    style={{ 
                      height: data.hours > 0 ? `${Math.max(data.percentage, 8)}%` : '4px',
                      minHeight: '4px'
                    }}
                    title={`${data.hours.toFixed(1)} hours`}
                  />
                  <span className="text-xs text-slate-500">{data.day}</span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-slate-800">
              {isLoading ? '...' : (weeklyProgress?.totalHours?.toFixed(1) || '0.0')}
            </div>
            <div className="text-xs text-slate-500">Hours This Week</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-slate-800">
              {isLoading ? '...' : (weeklyProgress?.completedVideos || 0)}
            </div>
            <div className="text-xs text-slate-500">Videos Completed</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
