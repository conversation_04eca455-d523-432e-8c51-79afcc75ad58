import { useState, useCallback } from "react";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Plus, Clock, Eye, MoreHorizontal, CheckCircle } from "lucide-react";

interface YouTubeVideo {
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  channelTitle: string;
  publishedAt: string;
  duration?: string;
  viewCount?: number;
}

interface VideoSearchProps {
  planId?: number;
  onVideoAdded?: () => void;
}

interface LearningPlan {
  id: number;
  title: string;
  slug: string;
}

export default function VideoSearch({ planId, onVideoAdded }: VideoSearchProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<YouTubeVideo[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [nextPageToken, setNextPageToken] = useState<string | null>(null);
  const [hasMoreResults, setHasMoreResults] = useState(false);
  const [selectedPlanId, setSelectedPlanId] = useState<string>("");
  const [isCreatingPlan, setIsCreatingPlan] = useState(false);
  const [newPlanTitle, setNewPlanTitle] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get user's learning plans for selection
  const { data: userPlans = [] } = useQuery<LearningPlan[]>({
    queryKey: ["/api/learning-plans"],
    enabled: !planId, // Only fetch if not in a specific plan context
  });

  // Get current plan videos to check for duplicates
  const { data: currentPlanVideos = [] } = useQuery({
    queryKey: [`/api/learning-plans/${planId || selectedPlanId}/videos`],
    enabled: !!(planId || selectedPlanId),
  });

  const searchMutation = useMutation({
    mutationFn: async ({ query, pageToken }: { query: string; pageToken?: string }) => {
      let url = `/api/youtube/search?q=${encodeURIComponent(query)}&maxResults=6`;
      if (pageToken) {
        url += `&pageToken=${pageToken}`;
      }
      
      const response = await apiRequest(url, { method: "GET" });
      return response;
    },
    onSuccess: (data, variables) => {
      const { pageToken } = variables;
      const videos = data.videos || data || [];
      
      if (pageToken) {
        // Append to existing results for "Load More"
        setSearchResults(prev => [...prev, ...videos]);
        setIsLoadingMore(false);
      } else {
        // Replace results for new search
        setSearchResults(videos);
        setIsSearching(false);
      }
      
      // Update pagination state
      setNextPageToken(data.nextPageToken || null);
      setHasMoreResults(!!data.nextPageToken);
    },
    onError: (error, variables) => {
      const { pageToken } = variables;
      if (pageToken) {
        setIsLoadingMore(false);
      } else {
        setIsSearching(false);
      }
      
      console.error('Search error:', error);
      
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      
      let errorMessage = error?.response?.data?.message || error?.message || "Failed to search videos";
      
      // Handle quota exceeded error with user-friendly message
      if (errorMessage.includes('quota') || errorMessage.includes('quotaExceeded')) {
        errorMessage = "YouTube search temporarily unavailable due to high usage. Please try again later or tomorrow.";
      }
      
      toast({
        title: "Search Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  // Create new plan mutation
  const createPlanMutation = useMutation({
    mutationFn: async (title: string) => {
      const response = await fetch("/api/learning-plans", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          title: title.trim(),
          description: `Learning plan created for organizing videos`,
          isPublic: false,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      // Parse the JSON response
      const data = await response.json();
      return data;
    },
    onSuccess: (response, title) => {
      // Invalidate and refetch queries to update the UI
      queryClient.invalidateQueries({ queryKey: ["/api/learning-plans"] });
      queryClient.refetchQueries({ queryKey: ["/api/learning-plans"] });
      
      // Set the selected plan ID from response
      const planId = response?.id || response?.data?.id;
      if (planId) {
        setSelectedPlanId(planId.toString());
      }
      
      setIsCreatingPlan(false);
      setNewPlanTitle("");
      
      // Use the title from the input since response might not have the expected format
      const planTitle = response?.title || title;
      toast({
        title: "Plan Created",
        description: `"${planTitle}" has been created and selected`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: "Failed to create plan. Please try again.",
        variant: "destructive",
      });
    },
  });

  const addVideoMutation = useMutation({
    mutationFn: async (video: YouTubeVideo) => {
      // First create the video
      const createdVideo = await apiRequest("/api/videos", {
        method: "POST",
        body: JSON.stringify(video)
      });
      
      // Add to selected plan
      const targetPlanId = planId || parseInt(selectedPlanId);
      if (targetPlanId && createdVideo.id) {
        await apiRequest(`/api/learning-plans/${targetPlanId}/videos`, {
          method: "POST",
          body: JSON.stringify({
            videoId: createdVideo.id
          })
        });
      }
      
      return createdVideo;
    },
    onSuccess: () => {
      const targetPlanId = planId || parseInt(selectedPlanId);
      const selectedPlan = userPlans.find(p => p.id === targetPlanId);
      const message = targetPlanId 
        ? `Video added to "${selectedPlan?.title || 'learning plan'}"` 
        : "Video added to your library";
      
      toast({
        title: "Success",
        description: message,
      });
      
      queryClient.invalidateQueries({ queryKey: ["/api/learning-plans"] });
      if (targetPlanId) {
        queryClient.invalidateQueries({ queryKey: [`/api/learning-plans/${targetPlanId}/videos`] });
      }
      onVideoAdded?.();
    },
    onError: (error: any) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      
      // Handle duplicate video error
      if (error?.response?.status === 409 || error?.response?.data?.code === "DUPLICATE_VIDEO") {
        toast({
          title: "Video Already Added",
          description: "This video is already in your learning plan.",
          variant: "destructive",
        });
        return;
      }
      
      toast({
        title: "Error",
        description: "Failed to add video. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Extract YouTube video ID from URL
  const extractYouTubeId = (url: string): string | null => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/,
      /youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/,
      /^([a-zA-Z0-9_-]{11})$/ // Direct video ID
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    return null;
  };

  const handleSearch = useCallback((query: string, isLoadMore = false) => {
    if (query.trim()) {
      // Check if it's a YouTube URL or video ID
      const videoId = extractYouTubeId(query.trim());
      
      if (videoId) {
        // Search by specific video ID
        setIsSearching(true);
        setNextPageToken(null);
        setHasMoreResults(false);
        searchByVideoId(videoId);
      } else {
        // Regular text search
        if (isLoadMore) {
          setIsLoadingMore(true);
          searchMutation.mutate({ query: query.trim(), pageToken: nextPageToken });
        } else {
          setIsSearching(true);
          setNextPageToken(null);
          setHasMoreResults(false);
          searchMutation.mutate({ query: query.trim() });
        }
      }
    } else {
      setSearchResults([]);
      setNextPageToken(null);
      setHasMoreResults(false);
    }
  }, [searchMutation, nextPageToken]);

  const searchByVideoId = async (videoId: string) => {
    try {
      const response = await apiRequest(`/api/youtube/video/${videoId}`, { method: "GET" });
      if (response) {
        setSearchResults([response]);
        setNextPageToken(null);
        setHasMoreResults(false);
      } else {
        setSearchResults([]);
        toast({
          title: "Video Not Found",
          description: "The YouTube video could not be found or is not available.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Video search error:', error);
      setSearchResults([]);
      toast({
        title: "Error",
        description: "Failed to fetch video details. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    
    // Debounce search
    const timeoutId = setTimeout(() => {
      handleSearch(value);
    }, 500);
    
    return () => clearTimeout(timeoutId);
  };

  const handleLoadMore = () => {
    if (nextPageToken && searchQuery.trim()) {
      handleSearch(searchQuery, true);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch(searchQuery);
    }
  };

  const formatViewCount = (count?: number) => {
    if (!count) return "0 views";
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M views`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K views`;
    return `${count} views`;
  };

  const formatDuration = (duration?: string) => {
    if (!duration) return "Unknown";
    // Parse ISO 8601 duration (PT#M#S) to readable format
    const match = duration.match(/PT(?:(\d+)M)?(?:(\d+)S)?/);
    if (match) {
      const minutes = parseInt(match[1] || "0");
      const seconds = parseInt(match[2] || "0");
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    return duration;
  };

  const isVideoInPlan = (youtubeId: string) => {
    return currentPlanVideos.some((pv: any) => pv.video.youtubeId === youtubeId);
  };

  return (
    <Card className="shadow-sm border border-slate-200">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-slate-800">
          Discover New Content
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Search Bar */}
        <div className="relative mb-6">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="w-5 h-5 text-slate-400" />
          </div>
          <Input
            type="text"
            placeholder="Search videos or paste YouTube URL (e.g., https://youtube.com/watch?v=...)"
            value={searchQuery}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            className="pl-10 h-12"
          />
          {isSearching && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </div>

        {/* Plan Selection (only show if not in a specific plan context) */}
        {!planId && userPlans.length > 0 && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-slate-700 mb-2">
              Select Learning Plan
            </label>
            <Select 
              value={selectedPlanId} 
              onValueChange={(value) => {
                if (value === "create-new") {
                  setIsCreatingPlan(true);
                } else {
                  setSelectedPlanId(value);
                }
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Choose a plan to add videos to" />
              </SelectTrigger>
              <SelectContent>
                {userPlans.map((plan) => (
                  <SelectItem key={plan.id} value={plan.id.toString()}>
                    {plan.title}
                  </SelectItem>
                ))}
                <SelectItem value="create-new" className="text-blue-600 font-medium">
                  + Create New Plan
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Search Results */}
        {searchResults.length > 0 && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-slate-800">
                Search Results ({searchResults.length} videos)
              </h3>
              {hasMoreResults && (
                <Badge variant="outline" className="text-xs">
                  More results available
                </Badge>
              )}
            </div>
            
            <div className="space-y-4">
              {searchResults.map((video, index) => (
                <div 
                  key={`${video.youtubeId}-${index}`} 
                  className="flex space-x-4 p-4 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors"
                >
                  <img 
                    src={video.thumbnailUrl || '/api/placeholder/200/150'} 
                    alt={video.title}
                    className="w-32 h-20 object-cover rounded-lg flex-shrink-0"
                  />
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-slate-800 mb-2 line-clamp-2">
                      {video.title}
                    </h4>
                    <p className="text-sm text-slate-600 mb-2 line-clamp-2">
                      {video.description}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-slate-500">
                      <span>{video.channelTitle}</span>
                      {video.duration && (
                        <div className="flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {formatDuration(video.duration)}
                        </div>
                      )}
                      {video.viewCount && (
                        <div className="flex items-center">
                          <Eye className="w-3 h-3 mr-1" />
                          {formatViewCount(video.viewCount)}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    {isVideoInPlan(video.youtubeId) ? (
                      <Button 
                        size="sm"
                        disabled
                        variant="outline"
                        className="text-green-600 border-green-600"
                      >
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Already Added
                      </Button>
                    ) : (
                      <Button 
                        size="sm"
                        onClick={() => addVideoMutation.mutate(video)}
                        disabled={addVideoMutation.isPending || (!planId && !selectedPlanId)}
                        className="bg-blue-500 hover:bg-blue-600 text-white disabled:opacity-50"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        {!planId && !selectedPlanId ? "Select Plan" : "Add to Plan"}
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            {/* Load More Button */}
            {hasMoreResults && (
              <div className="text-center pt-4">
                <Button
                  onClick={handleLoadMore}
                  disabled={isLoadingMore}
                  variant="outline"
                  size="lg"
                  className="px-8"
                >
                  {isLoadingMore ? (
                    <>
                      <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                      Loading more videos...
                    </>
                  ) : (
                    <>
                      <MoreHorizontal className="w-4 h-4 mr-2" />
                      Load More Videos
                    </>
                  )}
                </Button>
                <p className="text-xs text-slate-500 mt-2">
                  Showing {searchResults.length} videos
                </p>
              </div>
            )}
          </div>
        )}

        {searchQuery && !isSearching && !isLoadingMore && searchResults.length === 0 && (
          <div className="text-center py-8 text-slate-500">
            <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No videos found for "{searchQuery}"</p>
            <p className="text-sm">Try different keywords or check your spelling</p>
          </div>
        )}

        {!searchQuery && (
          <div className="text-center py-8 text-slate-500">
            <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Search for educational videos to add to your learning plans</p>
            <p className="text-sm">Try searching for topics like "React tutorial" or paste a YouTube URL</p>
          </div>
        )}
      </CardContent>
      
      {/* Create New Plan Dialog */}
      {isCreatingPlan && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-[90vw]">
            <h3 className="text-lg font-semibold mb-4">Create New Learning Plan</h3>
            <Input
              placeholder="Enter plan title (e.g., React Fundamentals)"
              value={newPlanTitle}
              onChange={(e) => setNewPlanTitle(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && newPlanTitle.trim()) {
                  createPlanMutation.mutate(newPlanTitle);
                }
              }}
              className="mb-4"
              autoFocus
            />
            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  setIsCreatingPlan(false);
                  setNewPlanTitle("");
                }}
                disabled={createPlanMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={() => createPlanMutation.mutate(newPlanTitle)}
                disabled={!newPlanTitle.trim() || createPlanMutation.isPending}
              >
                {createPlanMutation.isPending ? "Creating..." : "Create Plan"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
}
