import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Trophy, Star, Medal, Flame } from "lucide-react";

interface Achievement {
  id: number;
  type: string;
  title: string;
  description: string;
  earnedAt: string;
}

const achievementIcons = {
  streak: Flame,
  completion: Trophy,
  milestone: Star,
  default: Medal,
};

const achievementColors = {
  streak: 'text-orange-500 bg-orange-100',
  completion: 'text-emerald-500 bg-emerald-100',
  milestone: 'text-violet-500 bg-violet-100',
  default: 'text-blue-500 bg-blue-100',
};

export default function Achievements() {
  const { user } = useAuth();
  
  const { data: achievements = [], isLoading } = useQuery({
    queryKey: ["/api/achievements"],
    enabled: !!user,
  });

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return "Just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} min ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    return date.toLocaleDateString();
  };

  const getRecentAchievements = () => {
    if (achievements.length === 0) {
      // Return default achievements for empty state
      return [
        {
          id: 1,
          type: 'streak',
          title: 'Start Your Journey',
          description: 'Complete your first video to unlock achievements',
          earnedAt: new Date().toISOString(),
        },
        {
          id: 2,
          type: 'completion',
          title: 'Ready to Learn',
          description: 'Create your first learning plan',
          earnedAt: new Date().toISOString(),
        },
      ];
    }
    return achievements.slice(0, 3);
  };

  if (isLoading) {
    return (
      <Card className="shadow-sm border border-slate-200">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-slate-800">
            Recent Achievements
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3 animate-pulse">
                <div className="w-8 h-8 bg-slate-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-slate-200 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-slate-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const recentAchievements = getRecentAchievements();

  return (
    <Card className="shadow-sm border border-slate-200">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-slate-800">
          Recent Achievements
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {recentAchievements.map((achievement: Achievement) => {
            const IconComponent = achievementIcons[achievement.type as keyof typeof achievementIcons] || achievementIcons.default;
            const colorClass = achievementColors[achievement.type as keyof typeof achievementColors] || achievementColors.default;
            
            return (
              <div key={achievement.id} className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${colorClass}`}>
                  <IconComponent className="w-4 h-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-slate-800">
                    {achievement.title}
                  </p>
                  <p className="text-xs text-slate-500">
                    {achievement.description || formatTimeAgo(achievement.earnedAt)}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
        
        {achievements.length === 0 && (
          <div className="text-center mt-4 pt-4 border-t border-slate-200">
            <p className="text-xs text-slate-500">
              Complete videos and reach milestones to earn achievements!
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
