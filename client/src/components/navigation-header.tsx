import { useState } from "react";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ThemeToggle } from "./theme-toggle";
import NotificationsDropdown from "./notifications-dropdown";
import { GraduationCap, Menu } from "lucide-react";

export default function NavigationHeader() {
  const [location] = useLocation();
  const { user } = useAuth();
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  const isActive = (path: string) => location === path;

  const getInitials = (firstName?: string, lastName?: string) => {
    if (firstName && lastName) {
      return `${firstName[0]}${lastName[0]}`.toUpperCase();
    }
    if (firstName) {
      return firstName[0].toUpperCase();
    }
    return "U";
  };

  return (
    <nav className="bg-card border-b border-border sticky top-0 z-50 transition-colors">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-4">
            <Link href="/" className="flex items-center space-x-4">
              <div className="h-16 flex items-center justify-center">
                <img 
                  src="https://learniify-logo.s3.ap-south-1.amazonaws.com/logo.png" 
                  alt="Learniify Logo" 
                  className="h-12 w-auto object-contain max-w-[128px]"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.nextElementSibling?.classList.remove('hidden');
                  }}
                />
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-violet-500 rounded-lg flex items-center justify-center hidden">
                  <GraduationCap className="text-white w-8 h-8" />
                </div>
              </div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-500 via-violet-500 to-emerald-500 bg-clip-text text-transparent">
                Learniify
              </h1>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            <Link
              href="/"
              className={`font-medium transition-colors ${
                isActive("/")
                  ? "text-blue-500"
                  : "text-slate-600 hover:text-slate-900"
              }`}
            >
              Dashboard
            </Link>
            <Link
              href="/plans"
              className={`font-medium transition-colors ${
                isActive("/plans")
                  ? "text-blue-500"
                  : "text-slate-600 hover:text-slate-900"
              }`}
            >
              My Plans
            </Link>
            <Link
              href="/explore"
              className={`font-medium transition-colors ${
                isActive("/explore")
                  ? "text-blue-500"
                  : "text-slate-600 hover:text-slate-900"
              }`}
            >
              Explore
            </Link>
            <Link
              href="/progress"
              className={`font-medium transition-colors ${
                isActive("/progress")
                  ? "text-blue-500"
                  : "text-slate-600 hover:text-slate-900"
              }`}
            >
              Progress
            </Link>
            <Link
              href="/library"
              className={`font-medium transition-colors ${
                isActive("/library")
                  ? "text-blue-500"
                  : "text-slate-600 hover:text-slate-900"
              }`}
            >
              Library
            </Link>
            <Link
              href="/contact"
              className={`font-medium transition-colors ${
                isActive("/contact")
                  ? "text-blue-500"
                  : "text-slate-600 hover:text-slate-900"
              }`}
            >
              Contact
            </Link>
          </div>

          {/* User Profile and Notifications */}
          <div className="flex items-center space-x-4">
            <ThemeToggle />

            <NotificationsDropdown />

            {user && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="flex items-center space-x-3 h-auto p-2"
                  >
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={user.profileImageUrl || undefined} />
                      <AvatarFallback className="text-xs">
                        {getInitials(
                          user.firstName ?? undefined,
                          user.lastName ?? undefined
                        )}
                      </AvatarFallback>
                    </Avatar>
                    <span className="hidden sm:block text-sm font-medium text-slate-700 dark:text-slate-300">
                      {user.firstName || user.email}
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem asChild>
                    <Link href="/profile">Profile Settings</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/achievements">Achievements</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={async () => {
                      try {
                        await fetch("/api/auth/logout", {
                          method: "POST",
                          credentials: "include",
                        });
                        window.location.href = "/login";
                      } catch (error) {
                        console.error("Logout failed:", error);
                      }
                    }}
                  >
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setShowMobileMenu(!showMobileMenu)}
            >
              <Menu className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {showMobileMenu && (
          <div className="lg:hidden border-t border-border py-4">
            <div className="space-y-2">
              <Link
                href="/"
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  isActive("/")
                    ? "text-blue-500 bg-blue-50"
                    : "text-slate-600 hover:text-slate-900 hover:bg-slate-50"
                }`}
                onClick={() => setShowMobileMenu(false)}
              >
                Dashboard
              </Link>
              <Link
                href="/plans"
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  isActive("/plans")
                    ? "text-blue-500 bg-blue-50"
                    : "text-slate-600 hover:text-slate-900 hover:bg-slate-50"
                }`}
                onClick={() => setShowMobileMenu(false)}
              >
                My Plans
              </Link>
              <Link
                href="/explore"
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  isActive("/explore")
                    ? "text-blue-500 bg-blue-50"
                    : "text-slate-600 hover:text-slate-900 hover:bg-slate-50"
                }`}
                onClick={() => setShowMobileMenu(false)}
              >
                Explore
              </Link>
              <Link
                href="/progress"
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  isActive("/progress")
                    ? "text-blue-500 bg-blue-50"
                    : "text-slate-600 hover:text-slate-900 hover:bg-slate-50"
                }`}
                onClick={() => setShowMobileMenu(false)}
              >
                Progress
              </Link>
              <Link
                href="/library"
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  isActive("/library")
                    ? "text-blue-500 bg-blue-50"
                    : "text-slate-600 hover:text-slate-900 hover:bg-slate-50"
                }`}
                onClick={() => setShowMobileMenu(false)}
              >
                Library
              </Link>
              <Link
                href="/contact"
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  isActive("/contact")
                    ? "text-blue-500 bg-blue-50"
                    : "text-slate-600 hover:text-slate-900 hover:bg-slate-50"
                }`}
                onClick={() => setShowMobileMenu(false)}
              >
                Contact
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
