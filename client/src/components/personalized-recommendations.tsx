import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { isUnauthorizedError } from "@/lib/authUtils";
import { Play, Plus, Clock, Eye, Sparkles, FolderPlus } from "lucide-react";
import { Link } from "wouter";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState, useEffect } from "react";
import VideoCard from "@/components/video-card";

interface RecommendedVideo {
  id: number;
  youtubeId: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  duration: string;
  channelTitle: string;
  viewCount: number;
  publishedAt: string;
  relevanceScore: number;
}

interface UserPatterns {
  favoriteChannels: { channelTitle: string; watchCount: number }[];
  averageWatchTime: number;
  mostActiveHours: number[];
  totalVideosCompleted: number;
}

export default function PersonalizedRecommendations() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedVideo, setSelectedVideo] = useState<RecommendedVideo | null>(null);
  const [selectedPlanId, setSelectedPlanId] = useState<string>("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { data: recommendations = [], isLoading: recommendationsLoading, error: recommendationsError } = useQuery({
    queryKey: ['/api/recommendations'],
    retry: false,
  });
  
  // Auto-create all recommendation videos in DB for future use
  const createRecommendationVideosMutation = useMutation({
    mutationFn: async (videos: any[]) => {
      const createdVideos = [];
      for (const video of videos) {
        try {
          const createdVideo = await apiRequest("/api/videos", {
            method: "POST",
            body: JSON.stringify({
              youtubeId: video.youtubeId,
              title: video.title,
              description: video.description || '',
              thumbnailUrl: video.thumbnailUrl,
              duration: video.duration || 'PT0S',
              channelTitle: video.channelTitle,
              publishedAt: video.publishedAt,
              viewCount: video.viewCount || 0,
            })
          });
          createdVideos.push(createdVideo);
        } catch (error) {
          // Video creation failed, continue with others
        }
      }
      return createdVideos;
    },
  });
  
  // Auto-create videos when recommendations load
  useEffect(() => {
    if (recommendations.length > 0 && !createRecommendationVideosMutation.isPending) {
      createRecommendationVideosMutation.mutate(recommendations);
    }
  }, [recommendations]);
  
  // Debug logging
  console.log('🎯 Recommendations data:', { recommendations, isLoading: recommendationsLoading, error: recommendationsError });

  // Limit recommendations to 4 videos (1 main + 3 small)
  const limitedRecommendations = recommendations.slice(0, 4);

  const { data: patterns } = useQuery({
    queryKey: ['/api/user/patterns'],
    retry: false,
  });

  const { data: userPlans = [] } = useQuery({
    queryKey: ['/api/learning-plans'],
    retry: false,
  });



  const addToPlanMutation = useMutation({
    mutationFn: async ({ video, planId }: { video: RecommendedVideo; planId: number }) => {
      console.log('🚀 Adding existing video to plan:', video.title, 'Plan ID:', planId);
      
      // Video should already exist in DB, get it by YouTube ID
      const existingVideo = await apiRequest(`/api/videos/youtube/${video.youtubeId}`);
      
      // Add video to the plan using fetch (same as video player)
      const response = await fetch(`/api/learning-plans/${planId}/videos`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          videoId: existingVideo.id,
          orderIndex: 0
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to add video');
      }
      
      return data;
    },
    onSuccess: (data, variables) => {
      const selectedPlan = userPlans.find(p => p.id === variables.planId);
      
      if (data.code === 'ALREADY_EXISTS') {
        toast({
          title: "Already Added",
          description: `This video is already in "${selectedPlan?.title}"`,
          variant: "default"
        });
      } else {
        toast({
          title: "Success",
          description: `Video added to "${selectedPlan?.title}"`,
        });
      }
      
      setIsDialogOpen(false);
      setSelectedVideo(null);
      setSelectedPlanId("");
      

    },
    onError: (error: any) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      
      if (error?.response?.data?.code === 'INAPPROPRIATE_CONTENT') {
        toast({
          title: "Content Blocked",
          description: "This content is not suitable for our educational platform",
          variant: "destructive",
        });
        return;
      }
      
      toast({
        title: "Error",
        description: "Failed to add video. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleAddToPlan = (video: RecommendedVideo) => {
    setSelectedVideo(video);
    setIsDialogOpen(true);
  };

  const handleConfirmAdd = () => {
    if (selectedVideo && selectedPlanId) {
      addToPlanMutation.mutate({ video: selectedVideo, planId: parseInt(selectedPlanId) });
    }
  };



  const formatDuration = (duration: string) => {
    const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
    if (!match) return duration;
    
    const hours = match[1] ? parseInt(match[1]) : 0;
    const minutes = match[2] ? parseInt(match[2]) : 0;
    const seconds = match[3] ? parseInt(match[3]) : 0;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatViewCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M views`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K views`;
    }
    return `${count} views`;
  };

  if (recommendationsLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-purple-500" />
          <h2 className="text-xl font-semibold">Recommended for You</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(7)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <div className="aspect-video bg-gray-300 rounded-t-lg"></div>
              <CardContent className="p-4">
                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                <div className="h-3 bg-gray-300 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* User Insights */}
      {patterns && patterns.totalVideosCompleted > 0 && (
        <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <Sparkles className="w-5 h-5" />
              Your Learning Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-2xl font-bold text-purple-600">{patterns.totalVideosCompleted}</div>
                <div className="text-gray-600">Videos Completed</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">{Math.round(patterns.averageWatchTime / 60)}m</div>
                <div className="text-gray-600">Avg. Watch Time</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">{patterns.favoriteChannels.length}</div>
                <div className="text-gray-600">Favorite Channels</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {patterns.mostActiveHours.length > 0 ? `${patterns.mostActiveHours[0]}:00` : '--'}
                </div>
                <div className="text-gray-600">Peak Learning Hour</div>
              </div>
            </div>
            {patterns.favoriteChannels.length > 0 && (
              <div className="mt-4">
                <div className="text-sm font-medium text-gray-700 mb-2">Top Channels:</div>
                <div className="flex flex-wrap gap-2">
                  {patterns.favoriteChannels.slice(0, 3).map((channel, index) => (
                    <Badge key={index} variant="secondary" className="bg-purple-100 text-purple-700">
                      {channel.channelTitle} ({channel.watchCount})
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-purple-500" />
          <h2 className="text-xl font-semibold">
            {patterns && patterns.totalVideosCompleted > 0 
              ? "Recommended for You" 
              : "Popular Videos to Get Started"
            }
          </h2>
          {patterns && patterns.totalVideosCompleted > 0 && (
            <Badge variant="outline" className="text-xs">
              Based on your viewing history
            </Badge>
          )}
        </div>

        {recommendationsError ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Sparkles className="w-12 h-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to load recommendations</h3>
              <p className="text-gray-600 mb-4">
                There was an issue loading your recommendations. Please try refreshing the page or browse our video library.
              </p>
              <div className="flex gap-2 justify-center">
                <Button onClick={() => window.location.reload()}>Refresh</Button>
                <Link href="/library">
                  <Button variant="outline">Browse Library</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        ) : recommendations.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Sparkles className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No recommendations yet</h3>
              <p className="text-gray-600 mb-4">
                Complete a few videos to get personalized recommendations based on your interests.
              </p>
              <Link href="/library">
                <Button>Browse Video Library</Button>
              </Link>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {/* Main Featured Recommendation */}
            {limitedRecommendations.length > 0 && (
              <Card className="overflow-hidden hover:shadow-lg transition-shadow border-2 border-purple-200">
                <div className="md:flex">
                  <div className="relative md:w-1/2 aspect-video md:aspect-auto">
                    <img
                      src={limitedRecommendations[0].thumbnailUrl}
                      alt={limitedRecommendations[0].title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                      <Link href={`/video/${limitedRecommendations[0].id || limitedRecommendations[0].youtubeId}`}>
                        <Button size="lg" className="bg-white text-black hover:bg-gray-100">
                          <Play className="w-5 h-5 mr-2" />
                          Watch Now
                        </Button>
                      </Link>
                    </div>
                    <div className="absolute top-2 left-2">
                      <Badge className="bg-purple-500 text-white">
                        <Sparkles className="w-3 h-3 mr-1" />
                        Featured for You
                      </Badge>
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-sm">
                      {formatDuration(limitedRecommendations[0].duration)}
                    </div>
                  </div>
                  <CardContent className="md:w-1/2 p-6">
                    <h3 className="font-bold text-xl mb-3 line-clamp-2" title={limitedRecommendations[0].title}>
                      {limitedRecommendations[0].title}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">{limitedRecommendations[0].description}</p>
                    <p className="text-sm text-gray-500 mb-4">{limitedRecommendations[0].channelTitle}</p>
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center gap-1">
                        <Eye className="w-4 h-4" />
                        {formatViewCount(limitedRecommendations[0].viewCount)}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {formatDuration(limitedRecommendations[0].duration)}
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <Link href={`/video/${limitedRecommendations[0].id || limitedRecommendations[0].youtubeId}`} className="flex-1">
                        <Button size="lg" className="w-full">
                          <Play className="w-4 h-4 mr-2" />
                          Watch Now
                        </Button>
                      </Link>
                      <Button
                        size="lg"
                        variant="outline"
                        onClick={() => handleAddToPlan(limitedRecommendations[0])}
                        disabled={addToPlanMutation.isPending}
                      >
                        <Plus className="w-4 h-4 mr-1" />
                        Add to Plan
                      </Button>
                    </div>
                  </CardContent>
                </div>
              </Card>
            )}
            
            {/* Additional Recommendations Grid */}
            {limitedRecommendations.length > 1 && (
              <div>
                <h4 className="text-lg font-semibold mb-4 text-gray-700">More Recommendations</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {limitedRecommendations.slice(1, 4).map((video: RecommendedVideo) => (
                    <VideoCard
                      key={video.id || video.youtubeId}
                      video={video}
                      showProgress={false}
                      showFavoriteButton={true}
                      showAddToPlan={true}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Plan Selection Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add to Learning Plan</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Select a learning plan to add "{selectedVideo?.title}"
            </p>
            <Select value={selectedPlanId} onValueChange={setSelectedPlanId}>
              <SelectTrigger>
                <SelectValue placeholder="Select a learning plan" />
              </SelectTrigger>
              <SelectContent>
                {userPlans.map((plan: any) => (
                  <SelectItem key={plan.id} value={plan.id.toString()}>
                    {plan.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex gap-2 justify-end">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleConfirmAdd}
                disabled={!selectedPlanId || addToPlanMutation.isPending}
              >
                {addToPlanMutation.isPending ? "Adding..." : "Add to Plan"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}