import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNotifications, type Notification } from "@/hooks/useNotifications";
import { apiRequest } from "@/lib/queryClient";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuHeader,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Bell, 
  Users, 
  Trophy, 
  Share, 
  Play, 
  Target,
  Check,
  X,
  Eye
} from "lucide-react";
import { Link } from "wouter";

const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'friend_request':
    case 'friend_accepted':
      return <Users className="w-4 h-4 text-blue-500" />;
    case 'achievement':
      return <Trophy className="w-4 h-4 text-yellow-500" />;
    case 'plan_shared':
      return <Share className="w-4 h-4 text-green-500" />;
    case 'video_completed':
      return <Play className="w-4 h-4 text-purple-500" />;
    case 'milestone':
      return <Target className="w-4 h-4 text-orange-500" />;
    default:
      return <Bell className="w-4 h-4 text-gray-500" />;
  }
};

function NotificationItem({ notification }: { notification: Notification }) {
  const queryClient = useQueryClient();

  const markAsReadMutation = useMutation({
    mutationFn: async (notificationId: number) => {
      await apiRequest(`/api/notifications/${notificationId}/read`, { method: "PUT" });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
    },
  });

  const respondToFriendRequestMutation = useMutation({
    mutationFn: async ({ action, requestId }: { action: 'accept' | 'reject', requestId: number }) => {
      if (action === 'accept') {
        await apiRequest(`/api/friends/accept/${requestId}`, { method: "POST" });
      } else {
        await apiRequest(`/api/friends/reject/${requestId}`, { method: "DELETE" });
      }
    },
    onSuccess: () => {
      // Refresh notifications and friends list
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/friends'] });
      queryClient.invalidateQueries({ queryKey: ['/api/friends/requests'] });
    },
  });

  const handleMarkAsRead = () => {
    if (!notification.isRead) {
      markAsReadMutation.mutate(notification.id);
    }
  };

  const handleFriendRequest = (action: 'accept' | 'reject') => {
    if (notification.data?.requestId) {
      respondToFriendRequestMutation.mutate({
        action,
        requestId: notification.data.requestId
      });
    }
  };

  const handleFriendInviteAccept = async (inviteToken: string) => {
    try {
      await apiRequest('/api/friends/invite/accept', { method: 'POST', body: JSON.stringify({ inviteToken }) });
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
      queryClient.invalidateQueries({ queryKey: ['/api/friends'] });
      queryClient.invalidateQueries({ queryKey: ['/api/friends/requests'] });
    } catch (error) {
      console.error('Error accepting invite:', error);
    }
  };

  return (
    <div 
      className={`p-3 border-b border-gray-100 hover:bg-gray-50 transition-colors ${
        !notification.isRead ? 'bg-blue-50' : ''
      }`}
      onClick={handleMarkAsRead}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-1">
          {getNotificationIcon(notification.type)}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className="text-sm font-medium text-gray-900 truncate">
              {notification.title}
            </h4>
            {!notification.isRead && (
              <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
            )}
          </div>
          <p className="text-sm text-gray-600 mb-2">
            {notification.message}
          </p>
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-400">
              {new Date(notification.createdAt).toLocaleDateString()}
            </span>
            
            {/* Friend Request Actions */}
            {notification.type === 'friend_request' && notification.data?.requestId && (
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 px-2 text-xs bg-green-50 text-green-700 hover:bg-green-100"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleFriendRequest('accept');
                  }}
                  disabled={respondToFriendRequestMutation.isPending}
                >
                  <Check className="w-3 h-3 mr-1" />
                  {respondToFriendRequestMutation.isPending ? 'Accepting...' : 'Accept'}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 px-2 text-xs bg-red-50 text-red-600 hover:bg-red-100"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleFriendRequest('reject');
                  }}
                  disabled={respondToFriendRequestMutation.isPending}
                >
                  <X className="w-3 h-3 mr-1" />
                  {respondToFriendRequestMutation.isPending ? 'Declining...' : 'Decline'}
                </Button>
              </div>
            )}
            
            {/* Friend Invite Actions */}
            {notification.type === 'friend_invite' && notification.data?.inviteToken && (
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 px-2 text-xs bg-green-50 text-green-700 hover:bg-green-100"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleFriendInviteAccept(notification.data.inviteToken);
                  }}
                  disabled={respondToFriendRequestMutation.isPending}
                >
                  <Check className="w-3 h-3 mr-1" />
                  {respondToFriendRequestMutation.isPending ? 'Accepting...' : 'Accept'}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="h-6 px-2 text-xs bg-blue-50 text-blue-700 hover:bg-blue-100"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(`/invite/${notification.data.inviteToken}`, '_blank');
                  }}
                >
                  <Eye className="w-3 h-3 mr-1" />
                  View
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function NotificationsDropdown() {
  const { notifications, unreadCount, isLoading } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);
  const queryClient = useQueryClient();

  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      await apiRequest("/api/notifications/mark-all-read", { method: "PUT" });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/notifications'] });
    },
  });

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="w-5 h-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-80 p-0">
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900">Notifications</h3>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-blue-600 hover:text-blue-700"
                onClick={() => markAllAsReadMutation.mutate()}
                disabled={markAllAsReadMutation.isPending}
              >
                Mark all read
              </Button>
            )}
          </div>
        </div>

        <div className="max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 text-center text-gray-500">
              Loading notifications...
            </div>
          ) : notifications.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <Bell className="w-8 h-8 mx-auto mb-2 text-gray-300" />
              <p>No notifications yet</p>
            </div>
          ) : (
            notifications.map((notification: Notification) => (
              <NotificationItem key={notification.id} notification={notification} />
            ))
          )}
        </div>

        {notifications.length > 0 && (
          <div className="p-3 border-t border-gray-100">
            <Link href="/notifications">
              <Button variant="ghost" size="sm" className="w-full text-center">
                View All Notifications
              </Button>
            </Link>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}