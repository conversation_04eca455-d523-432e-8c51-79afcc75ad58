import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { isUnauthorizedError } from "@/lib/authUtils";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { UserPlus, Users, Mail, Check, X, Send, Trash2 } from "lucide-react";

interface Friend {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  profileImageUrl: string;
  friendshipId: number;
}

interface FriendRequest {
  id: number;
  requesterId: string;
  receiverId: string;
  status: string;
  createdAt: string;
  requester: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    profileImageUrl: string;
  };
}

interface FriendInvite {
  id: number;
  fromUserId: string;
  toEmail: string;
  inviteToken: string;
  status: string;
  createdAt: string;
}

export default function FriendsNetwork() {
  const [inviteEmail, setInviteEmail] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch friends
  const { data: friends = [], isLoading: friendsLoading } = useQuery({
    queryKey: ["/api/friends"],
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
      }
    },
  });

  // Fetch friend requests
  const { data: friendRequests = [], isLoading: requestsLoading } = useQuery({
    queryKey: ["/api/friends/requests"],
    retry: 1,
    onError: (error: Error) => {
      console.error('Friend requests error:', error);
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
      }
    },
  });

  // Fetch sent invites
  const { data: sentInvites = [], isLoading: invitesLoading } = useQuery({
    queryKey: ["/api/friends/invites"],
    retry: 1,
    onError: (error: Error) => {
      console.error('Friend invites error:', error);
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
      }
    },
  });

  // Fetch friend invites for current user
  const { data: friendInvites = [], isLoading: invitesForMeLoading } = useQuery({
    queryKey: ["/api/friends/invites-for-me"],
    retry: 1,
    onSuccess: (data) => {
      console.log('Friend invites for me data:', data);
    },
    onError: (error: Error) => {
      console.error('Friend invites for me error:', error);
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
      }
    },
  });

  console.log('Friend invites array:', friendInvites, 'Length:', friendInvites?.length);

  // Send friend invite
  const sendInviteMutation = useMutation({
    mutationFn: async (toEmail: string) => {
      return await apiRequest("/api/friends/invite", {
        method: "POST",
        body: JSON.stringify({ toEmail })
      });
    },
    onSuccess: (data: any) => {
      toast({
        title: data.emailSent ? "Invite Sent! 📧" : "Invite Created ⚠️",
        description: data.message || "Friend invite has been processed!",
        variant: data.emailSent ? "default" : "destructive",
      });
      setInviteEmail("");
      queryClient.invalidateQueries({ queryKey: ["/api/friends/invites"] });
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to send friend invite. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Accept friend request
  const acceptRequestMutation = useMutation({
    mutationFn: async (friendshipId: number) => {
      return await apiRequest(`/api/friends/accept/${friendshipId}`, {
        method: "POST"
      });
    },
    onSuccess: () => {
      toast({
        title: "Friend Request Accepted",
        description: "You are now friends!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/friends"] });
      queryClient.invalidateQueries({ queryKey: ["/api/friends/requests"] });
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to accept friend request.",
        variant: "destructive",
      });
    },
  });

  // Reject friend request
  const rejectRequestMutation = useMutation({
    mutationFn: async (friendshipId: number) => {
      return await apiRequest(`/api/friends/reject/${friendshipId}`, {
        method: "DELETE"
      });
    },
    onSuccess: () => {
      toast({
        title: "Friend Request Rejected",
        description: "Friend request has been rejected.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/friends/requests"] });
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to reject friend request.",
        variant: "destructive",
      });
    },
  });

  // Cancel friend invite
  const cancelInviteMutation = useMutation({
    mutationFn: async (inviteId: number) => {
      return await apiRequest(`/api/friends/invite/${inviteId}`, {
        method: "DELETE"
      });
    },
    onSuccess: () => {
      toast({
        title: "Invite Canceled",
        description: "Friend invite has been canceled.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/friends/invites"] });
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to cancel friend invite.",
        variant: "destructive",
      });
    },
  });

  // Delete friend invite (for cancelled/expired invites)
  const deleteInviteMutation = useMutation({
    mutationFn: async (inviteId: number) => {
      return await apiRequest(`/api/friends/invite/${inviteId}`, {
        method: "DELETE"
      });
    },
    onSuccess: () => {
      toast({
        title: "Invite Deleted",
        description: "Invite has been removed from your list.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/friends/invites"] });
    },
    onError: (error: Error) => {
      if (isUnauthorizedError(error)) {
        toast({
          title: "Unauthorized",
          description: "You are logged out. Logging in again...",
          variant: "destructive",
        });
        setTimeout(() => {
          window.location.href = "/api/login";
        }, 500);
        return;
      }
      toast({
        title: "Error",
        description: "Failed to delete invite.",
        variant: "destructive",
      });
    },
  });

  // Accept friend invite
  const acceptInviteMutation = useMutation({
    mutationFn: async (inviteToken: string) => {
      return await apiRequest(`/api/friends/invite/accept`, {
        method: "POST",
        body: JSON.stringify({ inviteToken })
      });
    },
    onSuccess: () => {
      toast({
        title: "Invite Accepted",
        description: "You are now friends!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/friends/invites-for-me"] });
      queryClient.invalidateQueries({ queryKey: ["/api/friends"] });
      queryClient.invalidateQueries({ queryKey: ["/api/notifications"] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: "Failed to accept invite.",
        variant: "destructive",
      });
    },
  });

  const handleAcceptInvite = (inviteToken: string) => {
    acceptInviteMutation.mutate(inviteToken);
  };

  const handleSendInvite = (e: React.FormEvent) => {
    e.preventDefault();
    if (inviteEmail.trim()) {
      sendInviteMutation.mutate(inviteEmail.trim());
    }
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ""}${lastName?.[0] || ""}`.toUpperCase() || "?";
  };

  const getDisplayName = (
    firstName?: string,
    lastName?: string,
    email?: string
  ) => {
    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    }
    return email || "Unknown User";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Friends Network
        </CardTitle>
        <CardDescription>
          Connect with other learners and build your learning network
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="friends" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="friends" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Friends ({friends.length})
            </TabsTrigger>
            <TabsTrigger value="requests" className="flex items-center gap-2">
              <UserPlus className="h-4 w-4" />
              Requests ({friendRequests.length + friendInvites.length})
            </TabsTrigger>
            <TabsTrigger value="invite" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Invite Friends
            </TabsTrigger>
          </TabsList>

          <TabsContent value="friends" className="space-y-4">
            {friendsLoading ? (
              <div className="text-center py-8">
                <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                <p className="text-sm text-gray-600">Loading friends...</p>
              </div>
            ) : friends.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-2">No friends yet</p>
                <p className="text-sm text-gray-500">
                  Start by inviting friends to join your learning journey!
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {friends.map((friend: Friend) => (
                  <div
                    key={friend.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={friend.profileImageUrl} />
                        <AvatarFallback>
                          {getInitials(friend.firstName, friend.lastName)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">
                          {getDisplayName(
                            friend.firstName,
                            friend.lastName,
                            friend.email
                          )}
                        </p>
                        <p className="text-sm text-gray-600">{friend.email}</p>
                      </div>
                    </div>
                    <Badge variant="secondary">Friend</Badge>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="requests" className="space-y-4">
            
            {requestsLoading || invitesForMeLoading ? (
              <div className="text-center py-8">
                <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                <p className="text-sm text-gray-600">Loading requests...</p>
              </div>
            ) : friendRequests.length === 0 && friendInvites.length === 0 ? (
              <div className="text-center py-8">
                <UserPlus className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No pending friend requests</p>
              </div>
            ) : (
              <div className="space-y-3">
                {/* Friend Requests */}
                {friendRequests.map((request: FriendRequest) => (
                  <div
                    key={request.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={request.requester.profileImageUrl} />
                        <AvatarFallback>
                          {getInitials(
                            request.requester.firstName,
                            request.requester.lastName
                          )}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">
                          {getDisplayName(
                            request.requester.firstName,
                            request.requester.lastName,
                            request.requester.email
                          )}
                        </p>
                        <p className="text-sm text-gray-600">
                          {request.requester.email}
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => acceptRequestMutation.mutate(request.id)}
                        disabled={acceptRequestMutation.isPending}
                      >
                        <Check className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => rejectRequestMutation.mutate(request.id)}
                        disabled={rejectRequestMutation.isPending}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
                
                {/* Friend Invites */}
                {friendInvites && friendInvites.length > 0 && friendInvites.map((invite: any) => {
                  console.log('Rendering invite:', invite);
                  return (
                  <div
                    key={`invite-${invite.id}`}
                    className="flex items-center justify-between p-3 border rounded-lg bg-blue-50"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-semibold">
                          {(invite.fromUserName || invite.fromUserEmail)?.charAt(0) || '?'}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium">
                          {invite.fromUserName || invite.fromUserEmail || 'Someone'}
                        </p>
                        <p className="text-sm text-gray-600">
                          Invited you via email
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => handleAcceptInvite(invite.inviteToken)}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Check className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(`/invite/${invite.inviteToken}`, '_blank')}
                      >
                        View
                      </Button>
                    </div>
                  </div>
                  );
                })}
              </div>
            )}
          </TabsContent>

          <TabsContent value="invite" className="space-y-4">
            <form onSubmit={handleSendInvite} className="space-y-4">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Email Address
                </label>
                <div className="flex gap-2">
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter friend's email address"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    required
                  />
                  <Button type="submit" disabled={sendInviteMutation.isPending}>
                    <Send className="h-4 w-4 mr-2" />
                    {sendInviteMutation.isPending
                      ? "Sending..."
                      : "Send Invite"}
                  </Button>
                </div>
              </div>
            </form>

            {/* Sent Invites */}
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Sent Invites</h4>
              {invitesLoading ? (
                <div className="text-center py-4">
                  <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
                </div>
              ) : sentInvites.length === 0 ? (
                <p className="text-sm text-gray-600">No invites sent yet</p>
              ) : (
                <div className="space-y-2">
                  {sentInvites.map((invite: FriendInvite) => (
                    <div
                      key={invite.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex-1">
                        <span className="text-sm font-medium">
                          {invite.toEmail}
                        </span>
                        <p className="text-xs text-gray-500">
                          Sent {new Date(invite.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            invite.status === "pending"
                              ? "secondary"
                              : invite.status === "accepted"
                              ? "default"
                              : invite.status === "canceled"
                              ? "destructive"
                              : "outline"
                          }
                        >
                          {invite.status}
                        </Badge>
                        {invite.status === "pending" ? (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              cancelInviteMutation.mutate(invite.id)
                            }
                            disabled={cancelInviteMutation.isPending}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            title="Cancel invite"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        ) : (invite.status === "canceled" || invite.status === "expired") && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              deleteInviteMutation.mutate(invite.id)
                            }
                            disabled={deleteInviteMutation.isPending}
                            className="text-gray-600 hover:text-red-700 hover:bg-red-50"
                            title="Delete invite"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
