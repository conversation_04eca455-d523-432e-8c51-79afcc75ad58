#!/bin/bash

# Database validation script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Database URLs
PROD_DB_URL="postgresql://postgres.pwaqpoqjmxxlcrxtbjkw:<EMAIL>:6543/postgres"
SECONDARY_DB_URL="postgresql://postgres.nxdobqmglaywashgaack:<EMAIL>:5432/postgres"

echo -e "${BLUE}🔍 Validating Database Backup Success${NC}"
echo "=============================================="

# Function to get table counts
get_table_counts() {
    local db_url=$1
    local db_name=$2
    
    echo -e "${YELLOW}📊 Checking $db_name database...${NC}"
    
    # Get counts for main tables
    users_count=$(psql "$db_url" -t -c "SELECT COUNT(*) FROM public.users;" 2>/dev/null | xargs)
    videos_count=$(psql "$db_url" -t -c "SELECT COUNT(*) FROM public.videos;" 2>/dev/null | xargs)
    learning_plans_count=$(psql "$db_url" -t -c "SELECT COUNT(*) FROM public.learning_plans;" 2>/dev/null | xargs)
    plan_videos_count=$(psql "$db_url" -t -c "SELECT COUNT(*) FROM public.plan_videos;" 2>/dev/null | xargs)
    video_progress_count=$(psql "$db_url" -t -c "SELECT COUNT(*) FROM public.video_progress;" 2>/dev/null | xargs)
    
    echo "  Users: $users_count"
    echo "  Videos: $videos_count"
    echo "  Learning Plans: $learning_plans_count"
    echo "  Plan Videos: $plan_videos_count"
    echo "  Video Progress: $video_progress_count"
    echo ""
    
    # Return counts as space-separated string
    echo "$users_count $videos_count $learning_plans_count $plan_videos_count $video_progress_count"
}

# Get counts from both databases
echo -e "${BLUE}Production Database:${NC}"
prod_counts=$(get_table_counts "$PROD_DB_URL" "Production")

echo -e "${BLUE}Secondary Database:${NC}"
secondary_counts=$(get_table_counts "$SECONDARY_DB_URL" "Secondary")

# Compare counts
echo -e "${YELLOW}🔄 Comparing Data...${NC}"
echo "=============================================="

if [ "$prod_counts" = "$secondary_counts" ]; then
    echo -e "${GREEN}✅ SUCCESS: All table counts match!${NC}"
    echo -e "${GREEN}   Backup was successful - data integrity verified${NC}"
else
    echo -e "${RED}❌ WARNING: Table counts don't match${NC}"
    echo "   Production:  $prod_counts"
    echo "   Secondary:   $secondary_counts"
fi

# Check if secondary database has any data at all
secondary_total=$(echo $secondary_counts | awk '{print $1+$2+$3+$4+$5}')
if [ "$secondary_total" -gt 0 ]; then
    echo -e "${GREEN}✅ Secondary database contains data${NC}"
else
    echo -e "${RED}❌ Secondary database appears to be empty${NC}"
fi

echo ""
echo -e "${BLUE}🎯 Validation Complete${NC}"