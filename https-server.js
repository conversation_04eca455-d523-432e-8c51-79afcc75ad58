import https from 'https';
import http from 'http';
import fs from 'fs';
import { spawn } from 'child_process';

// SSL options
const options = {
  key: fs.readFileSync('/home/<USER>/ssl/key.pem'),
  cert: fs.readFileSync('/home/<USER>/ssl/cert.pem')
};

// Start your existing app on port 3000
const app = spawn('node', ['--import', 'tsx/esm', 'server/index.ts'], {
  env: { ...process.env, PORT: '3000' },
  stdio: 'inherit'
});

// HTTP server (port 80) - redirect to HTTPS
const httpServer = http.createServer((req, res) => {
  res.writeHead(301, { Location: `https://${req.headers.host}${req.url}` });
  res.end();
});

// HTTPS server (port 443) - proxy to port 3000
const httpsServer = https.createServer(options, (req, res) => {
  const proxy = http.request({
    hostname: 'localhost',
    port: 3000,
    path: req.url,
    method: req.method,
    headers: req.headers
  }, (proxyRes) => {
    res.writeHead(proxyRes.statusCode, proxyRes.headers);
    proxyRes.pipe(res);
  });
  
  req.pipe(proxy);
});

// Start servers
httpServer.listen(80, () => console.log('HTTP server on port 80 (redirects to HTTPS)'));
httpsServer.listen(443, () => console.log('HTTPS server on port 443'));
