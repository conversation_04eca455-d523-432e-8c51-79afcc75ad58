#!/bin/bash

# Find the latest backup file
BACKUP_FILE=$(ls -t neon_data_backup_*.sql | head -n1)

if [ -z "$BACKUP_FILE" ]; then
    echo "No backup file found!"
    exit 1
fi

echo "Processing backup file: $BACKUP_FILE"

# Create a new file with conflict handling
OUTPUT_FILE="neon_ready_$(basename $BACKUP_FILE)"

# Add header to handle conflicts
cat > "$OUTPUT_FILE" << 'EOF'
-- Neon DB compatible data import with conflict handling
-- This will skip duplicate records instead of failing

SET session_replication_role = replica;

EOF

# Convert INSERT statements to INSERT ... ON CONFLICT DO NOTHING
sed 's/^INSERT INTO /INSERT INTO /g' "$BACKUP_FILE" | \
sed 's/);$/) ON CONFLICT DO NOTHING;/g' >> "$OUTPUT_FILE"

# Add footer
cat >> "$OUTPUT_FILE" << 'EOF'

SET session_replication_role = DEFAULT;

-- Update sequences to current max values
SELECT setval('achievements_id_seq', COALESCE((SELECT MAX(id) FROM achievements), 1));
SELECT setval('common_playlist_id_seq', COALESCE((SELECT MAX(id) FROM common_playlist), 1));
SELECT setval('friend_invites_id_seq', COALESCE((SELECT MAX(id) FROM friend_invites), 1));
SELECT setval('friendships_id_seq', COALESCE((SELECT MAX(id) FROM friendships), 1));
SELECT setval('group_activities_id_seq', COALESCE((SELECT MAX(id) FROM group_activities), 1));
SELECT setval('learning_adaptations_id_seq', COALESCE((SELECT MAX(id) FROM learning_adaptations), 1));
SELECT setval('learning_paths_id_seq', COALESCE((SELECT MAX(id) FROM learning_paths), 1));
SELECT setval('learning_plans_id_seq', COALESCE((SELECT MAX(id) FROM learning_plans), 1));
SELECT setval('password_reset_tokens_id_seq', COALESCE((SELECT MAX(id) FROM password_reset_tokens), 1));
SELECT setval('path_nodes_id_seq', COALESCE((SELECT MAX(id) FROM path_nodes), 1));
SELECT setval('plan_copies_id_seq', COALESCE((SELECT MAX(id) FROM plan_copies), 1));
SELECT setval('plan_likes_id_seq', COALESCE((SELECT MAX(id) FROM plan_likes), 1));
SELECT setval('plan_shares_id_seq', COALESCE((SELECT MAX(id) FROM plan_shares), 1));
SELECT setval('plan_videos_id_seq', COALESCE((SELECT MAX(id) FROM plan_videos), 1));
SELECT setval('study_group_members_id_seq', COALESCE((SELECT MAX(id) FROM study_group_members), 1));
SELECT setval('study_groups_id_seq', COALESCE((SELECT MAX(id) FROM study_groups), 1));
SELECT setval('user_playlist_selections_id_seq', COALESCE((SELECT MAX(id) FROM user_playlist_selections), 1));
SELECT setval('user_skill_profiles_id_seq', COALESCE((SELECT MAX(id) FROM user_skill_profiles), 1));
SELECT setval('video_difficulty_analysis_id_seq', COALESCE((SELECT MAX(id) FROM video_difficulty_analysis), 1));
SELECT setval('video_progress_id_seq', COALESCE((SELECT MAX(id) FROM video_progress), 1));
SELECT setval('videos_id_seq', COALESCE((SELECT MAX(id) FROM videos), 1));
EOF

echo "Created Neon-ready backup: $OUTPUT_FILE"
echo "To restore to Neon DB, run:"
echo "psql \"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require\" -f $OUTPUT_FILE"