#!/bin/bash

# Database Migration Runner for Learniify
# Usage: ./run-migrations.sh [migration_file]

set -e

# Database connection string
DB_URL="postgresql://postgres.pwaqpoqjmxxlcrxtbjkw:<EMAIL>:6543/postgres"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🚀 Learniify Database Migration Runner${NC}"
echo "=================================="

# Check if psql is installed
if ! command -v psql &> /dev/null; then
    echo -e "${RED}❌ Error: psql is not installed${NC}"
    echo "Please install PostgreSQL client tools"
    exit 1
fi

# Function to run a single migration
run_migration() {
    local file=$1
    echo -e "${YELLOW}📄 Running migration: $file${NC}"
    
    if psql "$DB_URL" -f "$file"; then
        echo -e "${GREEN}✅ Migration completed successfully: $file${NC}"
    else
        echo -e "${RED}❌ Migration failed: $file${NC}"
        exit 1
    fi
}

# If specific migration file provided
if [ $# -eq 1 ]; then
    migration_file="$1"
    if [ -f "$migration_file" ]; then
        run_migration "$migration_file"
    else
        echo -e "${RED}❌ Error: Migration file not found: $migration_file${NC}"
        exit 1
    fi
else
    # Run all migrations in order
    echo -e "${YELLOW}🔍 Looking for migration files...${NC}"
    
    migration_files=$(find migrations -name "*.sql" | sort)
    
    if [ -z "$migration_files" ]; then
        echo -e "${YELLOW}⚠️  No migration files found${NC}"
        exit 0
    fi
    
    echo -e "${GREEN}Found migrations:${NC}"
    echo "$migration_files"
    echo ""
    
    read -p "Do you want to run all migrations? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        for file in $migration_files; do
            run_migration "$file"
            echo ""
        done
        echo -e "${GREEN}🎉 All migrations completed successfully!${NC}"
    else
        echo -e "${YELLOW}Migration cancelled${NC}"
    fi
fi