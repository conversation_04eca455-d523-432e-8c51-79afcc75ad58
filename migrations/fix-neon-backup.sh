#!/bin/bash

# Find the latest backup file
BACKUP_FILE=$(ls -t neon_data_backup_*.sql | head -n1)

if [ -z "$BACKUP_FILE" ]; then
    echo "No backup file found!"
    exit 1
fi

echo "Processing backup file: $BACKUP_FILE"

# Create a new file with conflict handling
OUTPUT_FILE="neon_fixed_$(basename $BACKUP_FILE)"

# Add header
cat > "$OUTPUT_FILE" << 'EOF'
-- Neon DB compatible data import with conflict handling
SET session_replication_role = replica;

EOF

# Process the file line by line, only modify INSERT statements
while IFS= read -r line; do
    if [[ $line =~ ^INSERT\ INTO\ .* ]]; then
        # Only modify INSERT statements
        echo "$line" | sed 's/);$/) ON CONFLICT DO NOTHING;/g' >> "$OUTPUT_FILE"
    else
        # Keep other lines as-is
        echo "$line" >> "$OUTPUT_FILE"
    fi
done < "$BACKUP_FILE"

# Add footer
cat >> "$OUTPUT_FILE" << 'EOF'

SET session_replication_role = DEFAULT;
EOF

echo "Created fixed Neon backup: $OUTPUT_FILE"
echo "To restore to Neon DB, run:"
echo "psql \"postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require\" -f $OUTPUT_FILE"