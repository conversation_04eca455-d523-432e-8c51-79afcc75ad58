# Database Migrations

This directory contains SQL migration files for the Learniify database.

## Database Connections

**Production Database (6543):**
```bash
psql "postgresql://postgres.pwaqpoqjmxxlcrxtbjkw:<EMAIL>:6543/postgres"
```

**Development Database (5432):**
```bash
psql "**************************************************************/postgres"
```

## How to Apply Migrations

1. **Create a new migration file**: `migrations/YYYYMMDD_HHMMSS_description.sql`
2. **Test on development first**: `./db_migrate.sh dev migrations/your_file.sql`
3. **Apply to production**: `./db_migrate.sh prod migrations/your_file.sql`
4. **Document the change** in this README

## Migration Commands

```bash
# Test on development database
./db_migrate.sh dev migrations/20250801_add_new_column.sql

# Apply to production database
./db_migrate.sh prod migrations/20250801_add_new_column.sql
```

## Migration History

| Date | File | Description | Applied |
|------|------|-------------|---------|
| - | - | Initial database setup | ✅ |

## Important Notes

- ⚠️ **Always backup before applying migrations**
- ⚠️ **Test migrations on a copy first**
- ⚠️ **Migrations should be reversible when possible**
- ⚠️ **Never edit existing migration files**