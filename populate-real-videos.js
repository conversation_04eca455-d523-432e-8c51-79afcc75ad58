import { db } from './server/db.js';
import { commonPlaylist, videos, planVideos } from './shared/schema.js';

// Pre-selected high-quality YouTube videos for each category
const videoData = [
  // Programming
  {
    youtubeId: 'PkZNo7MFNFg',
    title: 'Learn JavaScript - Full Course for Beginners',
    description: 'This complete 134-part JavaScript tutorial for beginners will teach you everything you need to know to get started with the JavaScript programming language.',
    thumbnailUrl: 'https://i.ytimg.com/vi/PkZNo7MFNFg/maxresdefault.jpg',
    channelTitle: 'freeCodeCamp.org',
    category: 'Trending Skills',
    playlistTitle: 'Full Stack JavaScript Development',
    duration: 'PT3H26M13S'
  },
  {
    youtubeId: 'Ke90Tje7VS0',
    title: 'React Course - Beginner\'s Tutorial for React JavaScript Library [2022]',
    description: 'Learn React JS in this full course for beginners. React is a JavaScript library for building user interfaces.',
    thumbnailUrl: 'https://i.ytimg.com/vi/Ke90Tje7VS0/maxresdefault.jpg',
    channelTitle: 'freeCodeCamp.org',
    category: 'Trending Skills',
    playlistTitle: 'Modern React Development',
    duration: 'PT11H55M'
  },
  // Design
  {
    youtubeId: 'c9Wg6Cb_YlU',
    title: 'UI/UX Design Tutorial – Wireframe, Mockup & Design in Figma',
    description: 'Learn UI/UX design in this complete course. You will learn how to use Figma to create wireframes, mockups, and prototypes.',
    thumbnailUrl: 'https://i.ytimg.com/vi/c9Wg6Cb_YlU/maxresdefault.jpg',
    channelTitle: 'freeCodeCamp.org',
    category: 'Creative Skills',
    playlistTitle: 'UI/UX Design Fundamentals',
    duration: 'PT3H32M'
  },
  // Business
  {
    youtubeId: 'naIkpQ_cIt0',
    title: 'Digital Marketing Course Part - 1 🔥| Digital Marketing Tutorial For Beginners | Simplilearn',
    description: 'This digital marketing course will help you learn the fundamentals of digital marketing and help you get started.',
    thumbnailUrl: 'https://i.ytimg.com/vi/naIkpQ_cIt0/maxresdefault.jpg',
    channelTitle: 'Simplilearn',
    category: 'Practical Skills',
    playlistTitle: 'Digital Marketing Mastery',
    duration: 'PT4H15M'
  }
];

async function populateRealVideos() {
  console.log('🚀 Populating common playlist with real videos...');
  
  try {
    for (const videoInfo of videoData) {
      // Create video record
      const [video] = await db.insert(videos).values({
        youtubeId: videoInfo.youtubeId,
        title: videoInfo.title,
        description: videoInfo.description,
        thumbnailUrl: videoInfo.thumbnailUrl,
        channelTitle: videoInfo.channelTitle,
        duration: videoInfo.duration,
        publishedAt: new Date(),
        viewCount: 1000000
      }).onConflictDoNothing().returning();

      if (video) {
        console.log(`✅ Added video: ${video.title}`);
      }
    }
    
    console.log('🎉 Successfully populated playlist with real videos!');
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

populateRealVideos();