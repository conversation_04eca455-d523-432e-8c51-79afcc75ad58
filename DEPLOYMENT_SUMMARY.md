# Learnify AWS EC2 Deployment Summary

## Current Status
- **Server**: Running on EC2 (PM2 status: online)
- **Port**: 3000
- **Domain**: learniify.com
- **Issue**: Static files not being served (404 errors)

## Problem Identified
- React build creates files in `dist/public/index.html`
- Server looks for files in `dist/index.html`
- Path mismatch causing 404 "Not Found" errors

## Files Structure
```
dist/
├── index.js (server bundle - working)
└── public/
    ├── index.html (React app - exists but not served)
    ├── assets/
    │   ├── index-DpoLmyNr.js
    │   └── index-C1cKxHMO.css
    └── favicon files
```

## Server Configuration Issue
In `server/vite.ts`, the `serveStatic` function needs to point to correct path:
```typescript
// Current (wrong):
const distPath = path.resolve(process.cwd(), "dist");

// Should be:
const distPath = path.resolve(process.cwd(), "dist", "public");
```

## Google OAuth Setup
- **Client ID**: ************-65tg7l17r6ed5vak4e04hp6ev2e5k4uh.apps.googleusercontent.com
- **Domain**: learniify.com
- **Callback URL**: https://learniify.com/api/auth/google/callback

## Next Steps Needed
1. Fix server static file path configuration
2. Rebuild and deploy server
3. Test homepage access
4. Configure Nginx for domain routing
5. Set up SSL certificate
6. Update Google OAuth URLs

## Environment Variables (Production)
```bash
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://postgres.pwaqpoqjmxxlcrxtbjkw:<EMAIL>:6543/postgres
SESSION_SECRET=production-session-secret-32-chars-minimum-length-required-for-security
GOOGLE_CLIENT_ID=************-65tg7l17r6ed5vak4e04hp6ev2e5k4uh.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
YOUTUBE_API_KEY=***************************************
OPENAI_API_KEY=********************************************************************************************************************************************************************
```

## Current PM2 Status
- Process: learniify (online)
- Memory: ~65MB
- Command: `node --import tsx/esm server/index.ts`

## Quick Fix Command
```bash
# Copy files to where server expects them
cp dist/public/index.html dist/
cp -r dist/public/assets dist/
pm2 restart learniify
```

## Final Solution
Update `server/vite.ts` line 62 to use `dist/public` path, then rebuild and deploy.