// Simple test to check progress tracking
const fetch = require('node-fetch');

async function testProgress() {
  try {
    // Test the debug endpoint for a specific plan
    const response = await fetch('http://localhost:3000/api/debug/progress/1', {
      credentials: 'include',
      headers: {
        'Cookie': 'connect.sid=your-session-cookie' // You'll need to get this from browser
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('Progress Debug Data:', JSON.stringify(data, null, 2));
    } else {
      console.log('Response status:', response.status);
      console.log('Response text:', await response.text());
    }
  } catch (error) {
    console.error('Test error:', error);
  }
}

testProgress();