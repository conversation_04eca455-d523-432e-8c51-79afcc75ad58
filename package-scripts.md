# Required Package.json Scripts for AWS Amplify

Add these scripts to your `package.json` for proper Amplify deployment:

```json
{
  "scripts": {
    "dev": "NODE_ENV=development tsx server/index.ts",
    "build": "npm run build:client && npm run build:server",
    "build:client": "cd client && vite build",
    "build:server": "esbuild server/index.ts --bundle --platform=node --outfile=dist/server.js --external:pg-native --external:@neondatabase/serverless",
    "start": "node dist/server.js",
    "db:push": "drizzle-kit push",
    "db:generate": "drizzle-kit generate",
    "db:migrate": "drizzle-kit migrate"
  }
}
```

## Environment Variables for Production

Create a `.env.example` file:

```env
# Database
DATABASE_URL=****************************************/database

# Authentication
SESSION_SECRET=your-session-secret-here
REPLIT_DOMAINS=your-domain.com
ISSUER_URL=https://replit.com/oidc
REPL_ID=your-repl-id

# External APIs
YOUTUBE_API_KEY=your-youtube-api-key
SENDGRID_API_KEY=your-sendgrid-api-key
OPENAI_API_KEY=your-openai-api-key

# App Configuration
NODE_ENV=production
PORT=5000
```