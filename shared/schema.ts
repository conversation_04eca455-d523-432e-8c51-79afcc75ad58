import {
  pgTable,
  text,
  varchar,
  timestamp,
  jsonb,
  index,
  serial,
  integer,
  boolean,
  real,
  uniqueIndex,
} from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";

// Session storage table.
// (IMPORTANT) This table is mandatory for Replit Auth, don't drop it.
export const sessions = pgTable(
  "sessions",
  {
    sid: varchar("sid").primaryKey(),
    sess: jsonb("sess").notNull(),
    expire: timestamp("expire").notNull(),
  },
  (table) => [index("IDX_session_expire").on(table.expire)]
);

// User storage table.
// (IMPORTANT) This table is mandatory for Replit Auth, don't drop it.
export const users = pgTable("users", {
  id: varchar("id").primaryKey().notNull(),
  email: varchar("email").unique(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  profileImageUrl: varchar("profile_image_url"),
  passwordHash: varchar("password_hash"),
  isSupporter: boolean("is_supporter").default(false),
  supporterSince: timestamp("supporter_since"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const passwordResetTokens = pgTable("password_reset_tokens", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  token: varchar("token").notNull().unique(),
  expiresAt: timestamp("expires_at").notNull(),
  isUsed: boolean("is_used").default(false),
  createdAt: timestamp("created_at").defaultNow(),
});

export const learningPlans = pgTable("learning_plans", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id),
  title: text("title").notNull(),
  description: text("description"),
  slug: varchar("slug").notNull().unique(), // Professional URL identifier
  isActive: boolean("is_active").default(true),
  shareToken: varchar("share_token").unique(),
  isPublic: boolean("is_public").default(false), // Public vs Private plan
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const videos = pgTable("videos", {
  id: serial("id").primaryKey(),
  youtubeId: varchar("youtube_id").notNull().unique(),
  title: text("title").notNull(),
  description: text("description"),
  thumbnailUrl: text("thumbnail_url"),
  duration: varchar("duration"),
  channelTitle: text("channel_title"),
  publishedAt: timestamp("published_at"),
  viewCount: integer("view_count"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const planVideos = pgTable("plan_videos", {
  id: serial("id").primaryKey(),
  planId: integer("plan_id")
    .notNull()
    .references(() => learningPlans.id),
  videoId: integer("video_id")
    .notNull()
    .references(() => videos.id),
  orderIndex: integer("order_index").notNull(),
  addedAt: timestamp("added_at").defaultNow(),
});

export const videoProgress = pgTable(
  "video_progress",
  {
    id: serial("id").primaryKey(),
    userId: varchar("user_id")
      .notNull()
      .references(() => users.id),
    videoId: integer("video_id")
      .notNull()
      .references(() => videos.id),
    currentTime: real("current_time").default(0),
    isCompleted: boolean("is_completed").default(false),
    lastWatched: timestamp("last_watched").defaultNow(),
  },
  (table) => ({
    userVideoIdx: index("user_video_idx").on(table.userId, table.videoId),
  })
);

export const achievements = pgTable("achievements", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id),
  type: varchar("type").notNull(), // e.g., "streak", "completion", "milestone"
  title: text("title").notNull(),
  description: text("description"),
  earnedAt: timestamp("earned_at").defaultNow(),
});

// Friend system tables
export const friendships = pgTable("friendships", {
  id: serial("id").primaryKey(),
  requesterId: varchar("requester_id").notNull(),
  receiverId: varchar("receiver_id").notNull(),
  status: varchar("status").notNull().default("pending"), // pending, accepted, blocked
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const friendInvites = pgTable("friend_invites", {
  id: serial("id").primaryKey(),
  fromUserId: varchar("from_user_id").notNull(),
  toEmail: varchar("to_email").notNull(),
  inviteToken: varchar("invite_token").notNull().unique(),
  status: varchar("status").notNull().default("pending"), // pending, accepted, expired
  createdAt: timestamp("created_at").defaultNow(),
  expiresAt: timestamp("expires_at").notNull(),
});

// Plan sharing permissions for private plans
export const planShares = pgTable(
  "plan_shares",
  {
    id: serial("id").primaryKey(),
    planId: integer("plan_id")
      .notNull()
      .references(() => learningPlans.id, { onDelete: "cascade" }),
    sharedByUserId: varchar("shared_by_user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    sharedWithUserId: varchar("shared_with_user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    canAddVideos: boolean("can_add_videos").default(false),
    canReshare: boolean("can_reshare").default(false),
    createdAt: timestamp("created_at").defaultNow(),
    updatedAt: timestamp("updated_at").defaultNow(),
  },
  (table) => ({
    uniqueShare: uniqueIndex("unique_plan_share").on(
      table.planId,
      table.sharedWithUserId
    ),
  })
);

// Learning path visualization
export const learningPaths = pgTable("learning_paths", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull(),
  planId: integer("plan_id").notNull(),
  pathData: jsonb("path_data").notNull(), // stores node positions, connections, etc.
  completionPercentage: integer("completion_percentage").notNull().default(0),
  notes: text("notes").default(""), // User's notes for this learning path
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const pathNodes = pgTable("path_nodes", {
  id: serial("id").primaryKey(),
  pathId: integer("path_id").notNull(),
  videoId: integer("video_id").notNull(),
  position: jsonb("position").notNull(), // x, y coordinates
  isCompleted: boolean("is_completed").notNull().default(false),
  completedAt: timestamp("completed_at"),
  order: integer("order").notNull(),
});

// Social learning features
export const studyGroups = pgTable("study_groups", {
  id: serial("id").primaryKey(),
  name: varchar("name").notNull(),
  description: text("description"),
  createdById: varchar("created_by_id").notNull(),
  planId: integer("plan_id"), // optional shared learning plan
  isPrivate: boolean("is_private").notNull().default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const studyGroupMembers = pgTable("study_group_members", {
  id: serial("id").primaryKey(),
  groupId: integer("group_id").notNull(),
  userId: varchar("user_id").notNull(),
  role: varchar("role").notNull().default("member"), // member, moderator, admin
  joinedAt: timestamp("joined_at").defaultNow(),
});

export const groupActivities = pgTable("group_activities", {
  id: serial("id").primaryKey(),
  groupId: integer("group_id").notNull(),
  userId: varchar("user_id").notNull(),
  type: varchar("type").notNull(), // video_completed, plan_shared, achievement_earned
  content: jsonb("content").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

// Common playlist for all users
export const commonPlaylist = pgTable("common_playlist", {
  id: serial("id").primaryKey(),
  title: varchar("title").notNull(),
  description: text("description"),
  category: varchar("category").notNull(),
  difficulty: varchar("difficulty").notNull().default("Beginner"), // Beginner, Intermediate, Advanced
  estimatedDuration: varchar("estimated_duration"), // e.g., "2-3 hours", "1 week"
  youtubeSearchTerms: text("youtube_search_terms").array(), // Search terms for finding videos
  tags: text("tags").array(), // Tags for filtering
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// User's selections from common playlist
export const userPlaylistSelections = pgTable("user_playlist_selections", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").notNull(),
  playlistItemId: integer("playlist_item_id").notNull(),
  addedToPlanId: integer("added_to_plan_id"), // null if not added to any plan yet
  isBookmarked: boolean("is_bookmarked").notNull().default(false),
  createdAt: timestamp("created_at").defaultNow(),
});

// Favorites tables
export const favoriteVideos = pgTable("favorite_videos", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  videoId: integer("video_id")
    .notNull()
    .references(() => videos.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => ({
  uniqueFavoriteVideo: uniqueIndex("unique_favorite_video").on(
    table.userId,
    table.videoId
  ),
}));

export const favoritePlaylists = pgTable("favorite_playlists", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  playlistId: integer("playlist_id")
    .notNull()
    .references(() => learningPlans.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => ({
  uniqueFavoritePlaylist: uniqueIndex("unique_favorite_playlist").on(
    table.userId,
    table.playlistId
  ),
}));

// Plan likes table - Facebook-style thumbs up
export const planLikes = pgTable("plan_likes", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  planId: integer("plan_id")
    .notNull()
    .references(() => learningPlans.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => ({
  uniquePlanLike: uniqueIndex("unique_plan_like").on(
    table.userId,
    table.planId
  ),
}));

// AI Difficulty Optimizer tables
export const userSkillProfiles = pgTable("user_skill_profiles", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  subject: varchar("subject").notNull(), // e.g., "programming", "mathematics", "design"
  skillLevel: integer("skill_level").notNull().default(1), // 1-10 scale
  confidence: real("confidence").notNull().default(0.5), // 0-1 confidence in assessment
  lastAssessed: timestamp("last_assessed").defaultNow(),
  totalWatchTime: integer("total_watch_time").notNull().default(0), // in seconds
  completionRate: real("completion_rate").notNull().default(0), // 0-1
  strugglingTopics: jsonb("struggling_topics").default([]), // Topics user struggles with
  masteredTopics: jsonb("mastered_topics").default([]), // Topics user has mastered
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const videoDifficultyAnalysis = pgTable("video_difficulty_analysis", {
  id: serial("id").primaryKey(),
  videoId: integer("video_id")
    .notNull()
    .references(() => videos.id, { onDelete: "cascade" }),
  subject: varchar("subject").notNull(),
  difficultyLevel: integer("difficulty_level").notNull(), // 1-10 scale
  prerequisites: jsonb("prerequisites").default([]), // Required knowledge/skills
  learningObjectives: jsonb("learning_objectives").default([]), // What skills this teaches
  complexityFactors: jsonb("complexity_factors").default({}), // Pace, terminology, concepts
  estimatedCompletionTime: integer("estimated_completion_time"), // in minutes
  cognitiveLoad: integer("cognitive_load").notNull().default(5), // 1-10 scale
  analyzedAt: timestamp("analyzed_at").defaultNow(),
  confidence: real("confidence").notNull().default(0.8), // AI confidence in analysis
});

export const learningAdaptations = pgTable("learning_adaptations", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  videoId: integer("video_id")
    .notNull()
    .references(() => videos.id, { onDelete: "cascade" }),
  recommendedDifficulty: integer("recommended_difficulty").notNull(),
  actualDifficulty: integer("actual_difficulty"),
  userFeedback: integer("user_feedback"), // 1-5 rating of difficulty
  adaptationReason: text("adaptation_reason"),
  wasSkipped: boolean("was_skipped").default(false),
  strugglingIndicators: jsonb("struggling_indicators").default([]), // pause frequency, rewind count, etc.
  createdAt: timestamp("created_at").defaultNow(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  learningPlans: many(learningPlans),
  videoProgress: many(videoProgress),
  achievements: many(achievements),
  passwordResetTokens: many(passwordResetTokens),
  sentFriendRequests: many(friendships, { relationName: "sentRequests" }),
  receivedFriendRequests: many(friendships, {
    relationName: "receivedRequests",
  }),
  sentInvites: many(friendInvites),
  createdGroups: many(studyGroups),
  groupMemberships: many(studyGroupMembers),
  groupActivities: many(groupActivities),
  learningPaths: many(learningPaths),
  skillProfiles: many(userSkillProfiles),
  learningAdaptations: many(learningAdaptations),
  favoriteVideos: many(favoriteVideos),
  favoritePlaylists: many(favoritePlaylists),
  planLikes: many(planLikes),
}));

export const passwordResetTokensRelations = relations(
  passwordResetTokens,
  ({ one }) => ({
    user: one(users, {
      fields: [passwordResetTokens.userId],
      references: [users.id],
    }),
  })
);

export const learningPlansRelations = relations(
  learningPlans,
  ({ one, many }) => ({
    user: one(users, {
      fields: [learningPlans.userId],
      references: [users.id],
    }),
    planVideos: many(planVideos),
    planShares: many(planShares),
    favoritePlaylists: many(favoritePlaylists),
    planLikes: many(planLikes),
  })
);

export const planSharesRelations = relations(planShares, ({ one }) => ({
  plan: one(learningPlans, {
    fields: [planShares.planId],
    references: [learningPlans.id],
  }),
  sharedByUser: one(users, {
    fields: [planShares.sharedByUserId],
    references: [users.id],
    relationName: "sharedPlans",
  }),
  sharedWithUser: one(users, {
    fields: [planShares.sharedWithUserId],
    references: [users.id],
    relationName: "receivedPlans",
  }),
}));

export const videosRelations = relations(videos, ({ many }) => ({
  planVideos: many(planVideos),
  videoProgress: many(videoProgress),
  difficultyAnalysis: many(videoDifficultyAnalysis),
  learningAdaptations: many(learningAdaptations),
  favoriteVideos: many(favoriteVideos),
}));

export const planVideosRelations = relations(planVideos, ({ one }) => ({
  plan: one(learningPlans, {
    fields: [planVideos.planId],
    references: [learningPlans.id],
  }),
  video: one(videos, {
    fields: [planVideos.videoId],
    references: [videos.id],
  }),
}));

export const videoProgressRelations = relations(videoProgress, ({ one }) => ({
  user: one(users, {
    fields: [videoProgress.userId],
    references: [users.id],
  }),
  video: one(videos, {
    fields: [videoProgress.videoId],
    references: [videos.id],
  }),
}));

export const achievementsRelations = relations(achievements, ({ one }) => ({
  user: one(users, {
    fields: [achievements.userId],
    references: [users.id],
  }),
}));

// New relations for social features
export const friendshipsRelations = relations(friendships, ({ one }) => ({
  requester: one(users, {
    fields: [friendships.requesterId],
    references: [users.id],
    relationName: "sentRequests",
  }),
  receiver: one(users, {
    fields: [friendships.receiverId],
    references: [users.id],
    relationName: "receivedRequests",
  }),
}));

export const friendInvitesRelations = relations(friendInvites, ({ one }) => ({
  fromUser: one(users, {
    fields: [friendInvites.fromUserId],
    references: [users.id],
  }),
}));

export const learningPathsRelations = relations(
  learningPaths,
  ({ one, many }) => ({
    user: one(users, {
      fields: [learningPaths.userId],
      references: [users.id],
    }),
    plan: one(learningPlans, {
      fields: [learningPaths.planId],
      references: [learningPlans.id],
    }),
    nodes: many(pathNodes),
  })
);

export const pathNodesRelations = relations(pathNodes, ({ one }) => ({
  path: one(learningPaths, {
    fields: [pathNodes.pathId],
    references: [learningPaths.id],
  }),
  video: one(videos, {
    fields: [pathNodes.videoId],
    references: [videos.id],
  }),
}));

export const studyGroupsRelations = relations(studyGroups, ({ one, many }) => ({
  creator: one(users, {
    fields: [studyGroups.createdById],
    references: [users.id],
  }),
  plan: one(learningPlans, {
    fields: [studyGroups.planId],
    references: [learningPlans.id],
  }),
  members: many(studyGroupMembers),
  activities: many(groupActivities),
}));

export const studyGroupMembersRelations = relations(
  studyGroupMembers,
  ({ one }) => ({
    group: one(studyGroups, {
      fields: [studyGroupMembers.groupId],
      references: [studyGroups.id],
    }),
    user: one(users, {
      fields: [studyGroupMembers.userId],
      references: [users.id],
    }),
  })
);

export const groupActivitiesRelations = relations(
  groupActivities,
  ({ one }) => ({
    group: one(studyGroups, {
      fields: [groupActivities.groupId],
      references: [studyGroups.id],
    }),
    user: one(users, {
      fields: [groupActivities.userId],
      references: [users.id],
    }),
  })
);

// Favorites Relations
export const favoriteVideosRelations = relations(
  favoriteVideos,
  ({ one }) => ({
    user: one(users, {
      fields: [favoriteVideos.userId],
      references: [users.id],
    }),
    video: one(videos, {
      fields: [favoriteVideos.videoId],
      references: [videos.id],
    }),
  })
);

export const favoritePlaylistsRelations = relations(
  favoritePlaylists,
  ({ one }) => ({
    user: one(users, {
      fields: [favoritePlaylists.userId],
      references: [users.id],
    }),
    playlist: one(learningPlans, {
      fields: [favoritePlaylists.playlistId],
      references: [learningPlans.id],
    }),
  })
);

export const planLikesRelations = relations(
  planLikes,
  ({ one }) => ({
    user: one(users, {
      fields: [planLikes.userId],
      references: [users.id],
    }),
    plan: one(learningPlans, {
      fields: [planLikes.planId],
      references: [learningPlans.id],
    }),
  })
);

// AI Difficulty Optimizer Relations
export const userSkillProfilesRelations = relations(
  userSkillProfiles,
  ({ one }) => ({
    user: one(users, {
      fields: [userSkillProfiles.userId],
      references: [users.id],
    }),
  })
);

export const videoDifficultyAnalysisRelations = relations(
  videoDifficultyAnalysis,
  ({ one }) => ({
    video: one(videos, {
      fields: [videoDifficultyAnalysis.videoId],
      references: [videos.id],
    }),
  })
);

export const learningAdaptationsRelations = relations(
  learningAdaptations,
  ({ one }) => ({
    user: one(users, {
      fields: [learningAdaptations.userId],
      references: [users.id],
    }),
    video: one(videos, {
      fields: [learningAdaptations.videoId],
      references: [videos.id],
    }),
  })
);

// Insert schemas
export const insertLearningPlanSchema = createInsertSchema(learningPlans).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertVideoSchema = createInsertSchema(videos)
  .omit({
    id: true,
    createdAt: true,
  })
  .extend({
    publishedAt: z
      .union([
        z.string().transform((val) => new Date(val)),
        z.date(),
        z.undefined(),
      ])
      .optional(),
  });

export const insertPlanVideoSchema = createInsertSchema(planVideos)
  .omit({
    id: true,
    addedAt: true,
  })
  .extend({
    orderIndex: z.number().optional(),
  });

export const insertVideoProgressSchema = createInsertSchema(videoProgress).omit(
  {
    id: true,
    lastWatched: true,
  }
);

export const insertAchievementSchema = createInsertSchema(achievements).omit({
  id: true,
  earnedAt: true,
});

// New insert schemas for social features
export const insertFriendshipSchema = createInsertSchema(friendships).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertFriendInviteSchema = createInsertSchema(friendInvites).omit({
  id: true,
  createdAt: true,
});

export const insertLearningPathSchema = createInsertSchema(learningPaths).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertPathNodeSchema = createInsertSchema(pathNodes).omit({
  id: true,
  completedAt: true,
});

export const insertStudyGroupSchema = createInsertSchema(studyGroups).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertStudyGroupMemberSchema = createInsertSchema(
  studyGroupMembers
).omit({
  id: true,
  joinedAt: true,
});

export const insertGroupActivitySchema = createInsertSchema(
  groupActivities
).omit({
  id: true,
  createdAt: true,
});

// AI Difficulty Optimizer Schemas
export const insertUserSkillProfileSchema = createInsertSchema(
  userSkillProfiles
).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertVideoDifficultyAnalysisSchema = createInsertSchema(
  videoDifficultyAnalysis
).omit({
  id: true,
  analyzedAt: true,
});

export const insertLearningAdaptationSchema = createInsertSchema(
  learningAdaptations
).omit({
  id: true,
  createdAt: true,
});

// Common playlist schemas
export const insertCommonPlaylistSchema = createInsertSchema(
  commonPlaylist
).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  difficultyLevel: z.string().optional(), // Support both field names
});

export const insertUserPlaylistSelectionSchema = createInsertSchema(
  userPlaylistSelections
).omit({
  id: true,
  createdAt: true,
});

// Password reset token schema
export const insertPasswordResetTokenSchema = createInsertSchema(
  passwordResetTokens
).omit({
  id: true,
  createdAt: true,
});

// Plan sharing schema
export const insertPlanShareSchema = createInsertSchema(planShares).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Favorites schemas
export const insertFavoriteVideoSchema = createInsertSchema(favoriteVideos).omit({
  id: true,
  createdAt: true,
});

export const insertFavoritePlaylistSchema = createInsertSchema(favoritePlaylists).omit({
  id: true,
  createdAt: true,
});

export const insertPlanLikeSchema = createInsertSchema(planLikes).omit({
  id: true,
  createdAt: true,
});

// Types
export type UpsertUser = typeof users.$inferInsert;
export type User = typeof users.$inferSelect;
export type LearningPlan = typeof learningPlans.$inferSelect;
export type InsertLearningPlan = z.infer<typeof insertLearningPlanSchema>;
export type Video = typeof videos.$inferSelect;
export type InsertVideo = z.infer<typeof insertVideoSchema>;
export type PlanVideo = typeof planVideos.$inferSelect;
export type InsertPlanVideo = z.infer<typeof insertPlanVideoSchema>;
export type VideoProgress = typeof videoProgress.$inferSelect;
export type InsertVideoProgress = z.infer<typeof insertVideoProgressSchema>;
export type Achievement = typeof achievements.$inferSelect;
export type InsertAchievement = z.infer<typeof insertAchievementSchema>;

// New types for social features
export type Friendship = typeof friendships.$inferSelect;
export type InsertFriendship = z.infer<typeof insertFriendshipSchema>;
export type FriendInvite = typeof friendInvites.$inferSelect;
export type InsertFriendInvite = z.infer<typeof insertFriendInviteSchema>;
export type LearningPath = typeof learningPaths.$inferSelect;
export type InsertLearningPath = z.infer<typeof insertLearningPathSchema>;
export type PathNode = typeof pathNodes.$inferSelect;
export type InsertPathNode = z.infer<typeof insertPathNodeSchema>;
export type StudyGroup = typeof studyGroups.$inferSelect;
export type InsertStudyGroup = z.infer<typeof insertStudyGroupSchema>;
export type StudyGroupMember = typeof studyGroupMembers.$inferSelect;
export type InsertStudyGroupMember = z.infer<
  typeof insertStudyGroupMemberSchema
>;
export type GroupActivity = typeof groupActivities.$inferSelect;
export type InsertGroupActivity = z.infer<typeof insertGroupActivitySchema>;

// AI Difficulty Optimizer Types
export type UserSkillProfile = typeof userSkillProfiles.$inferSelect;
export type InsertUserSkillProfile = z.infer<
  typeof insertUserSkillProfileSchema
>;
export type VideoDifficultyAnalysis =
  typeof videoDifficultyAnalysis.$inferSelect;
export type InsertVideoDifficultyAnalysis = z.infer<
  typeof insertVideoDifficultyAnalysisSchema
>;
export type LearningAdaptation = typeof learningAdaptations.$inferSelect;
export type InsertLearningAdaptation = z.infer<
  typeof insertLearningAdaptationSchema
>;

// Common Playlist Types
export type CommonPlaylistItem = typeof commonPlaylist.$inferSelect & {
  difficultyLevel?: string; // Alias for difficulty field
};
export type InsertCommonPlaylistItem = z.infer<
  typeof insertCommonPlaylistSchema
>;
export type UserPlaylistSelection = typeof userPlaylistSelections.$inferSelect;
export type InsertUserPlaylistSelection = z.infer<
  typeof insertUserPlaylistSelectionSchema
>;

// Password Reset Token Types
export type PasswordResetToken = typeof passwordResetTokens.$inferSelect;
export type InsertPasswordResetToken = z.infer<
  typeof insertPasswordResetTokenSchema
>;

// Favorites Types
export type FavoriteVideo = typeof favoriteVideos.$inferSelect;
export type InsertFavoriteVideo = z.infer<typeof insertFavoriteVideoSchema>;
export type FavoritePlaylist = typeof favoritePlaylists.$inferSelect;
export type InsertFavoritePlaylist = z.infer<typeof insertFavoritePlaylistSchema>;
export type PlanLike = typeof planLikes.$inferSelect;
export type InsertPlanLike = z.infer<typeof insertPlanLikeSchema>;

