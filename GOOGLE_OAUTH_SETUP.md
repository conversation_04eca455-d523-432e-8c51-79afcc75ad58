# Google OAuth Setup Guide

## Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API (or Google People API)

## Step 2: Configure OAuth Consent Screen

1. Go to **APIs & Services** → **OAuth consent screen**
2. Choose **External** user type
3. Fill in required information:
   - App name: `LearnHub`
   - User support email: Your email
   - Developer contact information: Your email
4. Add scopes:
   - `../auth/userinfo.email`
   - `../auth/userinfo.profile`
5. Add test users (your email) for development

## Step 3: Create OAuth 2.0 Credentials

1. Go to **APIs & Services** → **Credentials**
2. Click **Create Credentials** → **OAuth 2.0 Client IDs**
3. Choose **Web application**
4. Configure:
   - Name: `LearnHub Web Client`
   - Authorized JavaScript origins:
     - `http://localhost:3000`
     - `https://your-domain.com` (for production)
   - Authorized redirect URIs:
     - `http://localhost:3000/api/auth/google/callback`
     - `https://your-domain.com/api/auth/google/callback` (for production)

## Step 4: Update Environment Variables

Copy the Client ID and Client Secret to your `.env` file:

```env
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here
```

## Step 5: Test the Integration

1. Start your development server: `npm run dev`
2. Visit: `http://localhost:3000/api/auth/google`
3. Complete the Google OAuth flow
4. You should be redirected to `/dashboard` after successful authentication

## Available Endpoints

- **Login**: `GET /api/auth/google`
- **Callback**: `GET /api/auth/google/callback`
- **User Info**: `GET /api/auth/user` (works with both Google and Replit auth)

## Frontend Integration

Add a Google login button to your frontend:

```jsx
<button onClick={() => window.location.href = '/api/auth/google'}>
  Sign in with Google
</button>
```

## Production Deployment

For AWS Amplify deployment:

1. Add your Amplify domain to authorized origins and redirect URIs
2. Set environment variables in Amplify Console:
   - `GOOGLE_CLIENT_ID`
   - `GOOGLE_CLIENT_SECRET`
3. Update OAuth consent screen with production domain

## Security Notes

- Keep your Client Secret secure and never expose it in frontend code
- Use HTTPS in production
- Regularly rotate your credentials
- Monitor OAuth usage in Google Cloud Console