#!/bin/bash

# Supabase Database Backup Script
# This will create a complete dump with schema and data

TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="supabase_backup_${TIMESTAMP}.sql"

echo "Creating complete backup from Supabase..."

pg_dump "postgresql://postgres.pwaqpoqjmxxlcrxtbjkw:<EMAIL>:6543/postgres" \
  --verbose \
  --clean \
  --no-acl \
  --no-owner \
  --format=plain \
  --file="${BACKUP_FILE}"

echo "Backup completed: ${BACKUP_FILE}"
echo "File size: $(du -h ${BACKUP_FILE} | cut -f1)"