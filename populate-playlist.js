import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { commonPlaylist } from './shared/schema.js';

const sql = postgres(process.env.DATABASE_URL);
const db = drizzle(sql);

const playlistData = [
  // Creative Skills
  { title: 'UI/UX Design Fundamentals', description: 'Master the principles of user interface and user experience design', category: 'Creative Skills', difficulty: 'Beginner', estimatedDuration: '3-4 weeks', youtubeSearchTerms: ['UI UX design tutorial', 'user interface design', 'figma tutorial', 'design principles'], tags: ['design', 'ui', 'ux', 'figma', 'creative'] },
  
  // Career Skills
  { title: 'Leadership and Management', description: 'Develop essential leadership and team management skills', category: 'Career Skills', difficulty: 'Intermediate', estimatedDuration: '2-3 weeks', youtubeSearchTerms: ['leadership skills', 'management training', 'team leadership', 'project management'], tags: ['leadership', 'management', 'team-building', 'career'] },
  
  // Trending Skills - DevOps
  { title: 'DevOps Fundamentals', description: 'Master CI/CD, containerization, and infrastructure automation', category: 'Trending Skills', difficulty: 'Intermediate', estimatedDuration: '6-8 weeks', youtubeSearchTerms: ['devops tutorial', 'docker kubernetes', 'jenkins ci cd', 'infrastructure as code'], tags: ['devops', 'docker', 'kubernetes', 'jenkins', 'ci-cd'] },
  
  // Trending Skills - Full Stack
  { title: 'Full Stack JavaScript Development', description: 'Master MERN/MEAN stack development from scratch', category: 'Trending Skills', difficulty: 'Intermediate', estimatedDuration: '8-12 weeks', youtubeSearchTerms: ['full stack javascript', 'MERN stack tutorial', 'react node express', 'javascript full stack'], tags: ['fullstack', 'javascript', 'react', 'nodejs', 'mern'] },
  
  // Trending Skills - Frontend
  { title: 'Modern React Development', description: 'Master React with hooks, context, and modern patterns', category: 'Trending Skills', difficulty: 'Intermediate', estimatedDuration: '4-6 weeks', youtubeSearchTerms: ['react tutorial 2024', 'react hooks', 'react context', 'modern react'], tags: ['react', 'hooks', 'context', 'frontend'] },
  
  // Trending Skills - Database
  { title: 'SQL Database Mastery', description: 'Master SQL queries, database design, and optimization', category: 'Trending Skills', difficulty: 'Beginner', estimatedDuration: '4-6 weeks', youtubeSearchTerms: ['sql tutorial', 'database design', 'mysql postgresql', 'sql queries'], tags: ['sql', 'database', 'mysql', 'postgresql'] },
  
  // Trending Skills - Cloud Computing
  { title: 'AWS Cloud Fundamentals', description: 'Master Amazon Web Services core services and architecture', category: 'Trending Skills', difficulty: 'Beginner', estimatedDuration: '6-8 weeks', youtubeSearchTerms: ['aws tutorial', 'amazon web services', 'aws fundamentals', 'cloud computing aws'], tags: ['aws', 'cloud', 'amazon', 'cloud-computing'] },
  
  // Trending Skills - Cybersecurity
  { title: 'Cybersecurity Fundamentals', description: 'Essential cybersecurity concepts and threat landscape', category: 'Trending Skills', difficulty: 'Beginner', estimatedDuration: '4-6 weeks', youtubeSearchTerms: ['cybersecurity basics', 'information security', 'cyber threats', 'security fundamentals'], tags: ['cybersecurity', 'security', 'threats', 'fundamentals'] }
];

async function populatePlaylist() {
  try {
    await db.insert(commonPlaylist).values(playlistData);
    console.log('✅ Common playlist populated successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error populating playlist:', error);
    process.exit(1);
  }
}

populatePlaylist();