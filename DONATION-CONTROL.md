# 💝 Donation Feature Control Guide

## 🔒 Current Status: **DISABLED**

The donation feature is currently hidden from users via environment variable control.

---

## 🎛️ How to Enable/Disable Donations

### Method 1: Environment Variable (Recommended)

**To ENABLE donations:**
1. Open `.env` file
2. Change `VITE_ENABLE_DONATIONS=false` to `VITE_ENABLE_DONATIONS=true`
3. Restart the development server

**To DISABLE donations:**
1. Open `.env` file
2. Change `VITE_ENABLE_DONATIONS=true` to `VITE_ENABLE_DONATIONS=false`
3. Restart the development server

### Method 2: Browser Console (Legacy)

**Note:** This method is deprecated but still works for testing.
```javascript
localStorage.setItem('enableDonations', 'true');
// Then refresh the page
```

---

## 📋 What Gets Hidden/Shown

### ❌ When DISABLED (Current State):
- ✅ Bottom donation banner
- ✅ Footer "Support Us" link
- ✅ `/donate` route access
- ✅ All donation prompts

### ✅ When ENABLED:
- ✅ Horizontal donation banner at bottom
- ✅ Footer donation link
- ✅ Full donation page access
- ✅ Supporter status tracking

---

## 🔧 Technical Implementation

### Files Modified:
- `client/src/components/donation-banner.tsx` - Banner visibility
- `client/src/components/footer.tsx` - Footer link
- `client/src/App.tsx` - Route access
- `.env` - Feature flag (`VITE_ENABLE_DONATIONS`)

### Feature Flag Logic:
```javascript
const donationsEnabled = import.meta.env.VITE_ENABLE_DONATIONS === 'true';
```

---

## 🚀 Quick Actions

### Enable Donations Now:
```bash
# Edit .env file:
VITE_ENABLE_DONATIONS=true
# Then restart server
```

### Disable Donations:
```bash
# Edit .env file:
VITE_ENABLE_DONATIONS=false
# Then restart server
```

---

## 📝 Notes

- **All other features remain active** (learning, progress, AI, etc.)
- **Database supporter tracking** still works
- **Server restart required** - changes take effect after restart
- **Global setting** - affects all users of the application
- **Safe to enable** - legal analysis shows it's compliant

---

## ⚖️ Legal Status

Based on analysis:
- ✅ **SAFE** to monetize platform features
- ✅ **SAFE** to accept donations for server costs
- ✅ **SAFE** to charge for premium features
- ❌ **NOT SAFE** to monetize YouTube videos directly

**Recommendation:** You can safely enable donations anytime.

---

## 🎯 When Ready to Go Live

1. Set `VITE_ENABLE_DONATIONS=true` in `.env` file
2. Restart the development server
3. Test donation flow
4. Update any marketing materials
5. Monitor user feedback

**The feature is ready - just waiting for your green light!** 🚀