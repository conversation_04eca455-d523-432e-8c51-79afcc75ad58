# Supabase Database Setup

## Get Your Correct Connection String

1. **Go to your Supabase Dashboard**
   - Visit: https://supabase.com/dashboard
   - Select your project: `pwaqpoqjmxxlcrxtbjkw`

2. **Get Database Connection Details**
   - Go to Settings → Database
   - Look for "Connection string" section
   - Copy the "URI" format connection string

3. **Common Supabase Connection Formats**
   ```
   # Direct connection (most common):
   postgresql://postgres:[PASSWORD]@db.[PROJECT_REF].supabase.co:5432/postgres
   
   # Pooler connection (for high traffic):
   postgresql://postgres.[PROJECT_REF]:[PASSWORD]@aws-0-[REGION].pooler.supabase.com:6543/postgres
   ```

4. **Your Project Details**
   - Project Reference: `pwaqpoqjmxxlcrxtbjkw`
   - Password: `LearnHub2025`
   - Likely correct format: `postgresql://postgres:<EMAIL>:5432/postgres`

## Update Your .env File

Replace the DATABASE_URL in your `.env` file with the correct connection string from your Supabase dashboard.

## Test Connection

Run: `node test-db-connection.js` to verify the connection works.

## Next Steps

Once connected:
1. Run `npm run db:push` to sync your schema
2. Run `npm run dev` to start local development
3. Your app should connect to Supabase successfully