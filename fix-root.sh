#!/bin/bash

# Root-Based Build Structure Fix Script
# Run as root user anytime you face build issues

set -e

echo "🔧 Root-Based Build Structure Fix..."

# Auto-detect application directory
APP_DIR=""
if [ -d "/root/learniify" ]; then
    APP_DIR="/root/learniify"
elif [ -d "/home/<USER>/learniify" ]; then
    APP_DIR="/home/<USER>/learniify"
elif [ -f "package.json" ]; then
    APP_DIR="$(pwd)"
else
    echo "❌ Cannot find learniify directory!"
    exit 1
fi

cd "$APP_DIR"
echo "📁 Working in: $(pwd)"

# Stop PM2 processes
echo "⏹️  Stopping PM2 processes..."
pm2 stop all 2>/dev/null || true

# Function to fix build structure
fix_build_structure() {
    echo "🔍 Analyzing build structure..."
    
    if [ -f "dist/index.html" ]; then
        echo "✅ dist/index.html exists - structure is correct"
        return 0
    elif [ -f "dist/public/index.html" ]; then
        echo "📋 Copying from dist/public/ to dist/"
        cp dist/public/index.html dist/index.html
        cp -r dist/public/assets dist/ 2>/dev/null || true
        echo "✅ Fixed: dist/public/ → dist/"
        return 0
    elif [ -f "client/dist/index.html" ]; then
        echo "📋 Copying from client/dist/ to dist/"
        mkdir -p dist
        cp client/dist/index.html dist/index.html
        cp -r client/dist/assets dist/ 2>/dev/null || true
        echo "✅ Fixed: client/dist/ → dist/"
        return 0
    else
        echo "❌ No index.html found - need to rebuild"
        return 1
    fi
}

# Try to fix existing build
if fix_build_structure; then
    echo "✅ Build structure fixed!"
else
    echo "🔨 Rebuilding application..."
    rm -rf dist/ client/dist/ node_modules/.cache/ 2>/dev/null || true
    npm install
    npm run build
    
    if ! fix_build_structure; then
        echo "❌ Rebuild failed"
        exit 1
    fi
fi

# Verify final state
if [ ! -f "dist/index.html" ]; then
    echo "❌ Fix failed - dist/index.html missing"
    exit 1
fi

echo "✅ Verified: dist/index.html exists"

# Start PM2
echo "🚀 Starting PM2..."
pm2 start npm --name "learniify-http" -- start 2>/dev/null || pm2 restart learniify-http

sleep 3

# Test
echo "🧪 Testing..."
if curl -f -s -I http://localhost:3000 > /dev/null; then
    echo "✅ SUCCESS: Server responding!"
else
    echo "⚠️  Check logs: pm2 logs"
fi

pm2 status
echo "🎉 Root fix completed!"