import https from 'https';
import fs from 'fs';
import httpProxy from 'http-proxy';

const options = {
  key: fs.readFileSync('/home/<USER>/ssl/key.pem'),
  cert: fs.readFileSync('/home/<USER>/ssl/cert.pem')
};

const proxy = httpProxy.createProxyServer({});

const server = https.createServer(options, (req, res) => {
  proxy.web(req, res, { target: 'http://localhost:3000' });
});

server.listen(3001, () => console.log('HTTPS proxy on port 3001'));
