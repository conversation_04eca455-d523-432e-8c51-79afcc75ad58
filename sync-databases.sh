#!/bin/bash
set -e

# Database URLs
PROD="postgresql://postgres.pwaqpoqjmxxlcrxtbjkw:<EMAIL>:6543/postgres"
SECONDARY="postgresql://postgres.nxdobqmglaywashgaack:<EMAIL>:5432/postgres"

# Backup filename with timestamp
BACKUP="backup_$(date +%Y%m%d_%H%M%S).sql"

echo "🚀 Starting database sync..."

# Create backup
echo "📦 Creating backup..."
pg_dump "$PROD" --clean --no-acl --no-owner --file="$BACKUP"
echo "✅ Backup created: $BACKUP"

# Restore to secondary
echo "🔄 Restoring to secondary..."
psql "$SECONDARY" --quiet --file="$BACKUP"
echo "✅ Restore completed"

# Validate
echo "🔍 Validating..."
PROD_COUNT=$(psql "$PROD" -t -c "SELECT COUNT(*) FROM public.users;" | xargs)
SEC_COUNT=$(psql "$SECONDARY" -t -c "SELECT COUNT(*) FROM public.users;" | xargs)

if [ "$PROD_COUNT" = "$SEC_COUNT" ]; then
    echo "✅ Validation passed: $PROD_COUNT users synced"
else
    echo "⚠️  User count mismatch: $PROD_COUNT vs $SEC_COUNT"
fi

# Cleanup
rm "$BACKUP"
echo "🎉 Sync complete!"