import express, { type Express } from "express";
import fs from "fs";
import path from "path";
import { createServer as createViteServer, createLogger } from "vite";
import { type Server } from "http";
import viteConfig from "../vite.config";
import { nanoid } from "nanoid";
import { sanitizePath, sanitizeForLog } from "./security";

const viteLogger = createLogger();

export function log(message: string, source = "express") {
  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  console.log(`${formattedTime} [${source}] ${message}`);
}

export async function setupVite(app: Express, server: Server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true as const,
  };

  const vite = await createViteServer({
    ...viteConfig,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      },
    },
    server: serverOptions,
    appType: "custom",
  });

  app.use(vite.middlewares);
  app.use("*", async (req, res, next) => {
    // Sanitize URL to prevent path traversal
    const url = sanitizePath(req.originalUrl) || '/';

    try {
      const clientTemplate = path.resolve(
        process.cwd(),
        "client",
        "index.html"
      );
      
      // Validate that the resolved path is within expected directory
      const expectedDir = path.resolve(process.cwd(), "client");
      if (!clientTemplate.startsWith(expectedDir)) {
        throw new Error("Invalid file path");
      }

      // always reload the index.html file from disk incase it changes
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e as Error);
      console.error(sanitizeForLog('Vite error:'), e);
      next(e);
    }
  });
}

export function serveStatic(app: Express) {
  const distPath = path.resolve(process.cwd(), "dist");
  const publicPath = path.resolve(distPath, "public");

  // Check if we have the expected public directory structure
  const clientPath = fs.existsSync(publicPath) ? publicPath : distPath;
  const indexPath = path.resolve(clientPath, "index.html");

  if (!fs.existsSync(indexPath)) {
    const errorMsg = `Could not find index.html in: ${clientPath}, make sure to build the client first`;
    console.error(sanitizeForLog(errorMsg));
    throw new Error(errorMsg);
  }

  app.use(express.static(clientPath));

  // Only serve index.html for non-API routes
  app.use((req, res, next) => {
    if (req.path.startsWith("/api/")) {
      return next();
    }
    
    // Validate path to prevent directory traversal
    const safePath = sanitizePath(req.path);
    if (!safePath || safePath.includes('..')) {
      return res.status(400).send('Invalid path');
    }
    
    res.sendFile(indexPath);
  });
}
