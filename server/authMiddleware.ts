import type { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { isAuthenticated as replitAuth } from "./replitAuth";

// Unified authentication middleware that works with both Google and Replit auth
export const isAuthenticated: RequestHandler = async (req, res, next) => {
  // Check if user is authenticated via Google OAuth
  if (req.isAuthenticated() && req.user) {
    const user = req.user as any;
    
    // For Google OAuth users, create a compatible user object
    if (user.id && !user.claims) {
      req.user = {
        ...user,
        claims: {
          sub: user.id,
          email: user.email,
          first_name: user.firstName,
          last_name: user.lastName,
          profile_image_url: user.profileImageUrl
        }
      };
    }
    
    return next();
  }

  // Fall back to Replit authentication
  return replitAuth(req, res, next);
};