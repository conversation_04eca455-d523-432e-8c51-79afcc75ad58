import nodemailer from "nodemailer";

// Check if WorkMail is configured
const W<PERSON><PERSON><PERSON><PERSON>_SMTP_HOST = process.env.WOR<PERSON><PERSON>IL_SMTP_HOST;
const WORKMAIL_SMTP_PORT = process.env.WORKMAIL_SMTP_PORT;
const WOR<PERSON><PERSON>IL_EMAIL = process.env.WORKMAIL_EMAIL;
const WORKMAIL_PASSWORD = process.env.WORKMAIL_PASSWORD;
const isDevelopment = process.env.NODE_ENV !== "production";

// Create nodemailer transporter for Amazon WorkMail
let transporter: nodemailer.Transporter | null = null;

if (
  WOR<PERSON><PERSON>IL_SMTP_HOST &&
  WORKMAIL_SMTP_PORT &&
  WORKMAIL_EMAIL &&
  WORKMAIL_PASSWORD
) {
  transporter = nodemailer.createTransport({
    host: WORK<PERSON>IL_SMTP_HOST,
    port: parseInt(WORKMAIL_SMTP_PORT),
    secure: true, // Use SSL
    auth: {
      user: WORKMAIL_EMAIL,
      pass: WORKMAIL_PASSWORD,
    },
    tls: {
      rejectUnauthorized: false, // For development, you might need this
    },
  });

  console.log("✅ Amazon WorkMail SMTP configured successfully");
} else {
  if (isDevelopment) {
    console.warn(
      "⚠️ WorkMail SMTP not fully configured - emails will be simulated in development"
    );
  } else {
    console.error("❌ WorkMail SMTP configuration incomplete in production");
  }
}

interface EmailParams {
  to: string;
  from: string;
  subject: string;
  text?: string;
  html?: string;
  replyTo?: string;
}

export async function sendEmail(params: EmailParams): Promise<boolean> {
  // If no WorkMail configured in development, simulate email sending
  if (!transporter && isDevelopment) {
    console.log("📧 [SIMULATED EMAIL] Would send email:");
    console.log(`   To: ${params.to}`);
    console.log(`   From: ${params.from}`);
    console.log(`   Subject: ${params.subject}`);
    if (params.replyTo) {
      console.log(`   Reply-To: ${params.replyTo}`);
    }
    console.log(`   Text: ${params.text?.substring(0, 100)}...`);
    console.log("📧 [SIMULATED EMAIL] Email simulation complete");
    return true; // Return true to simulate successful sending
  }

  // If no transporter in production, return error
  if (!transporter) {
    console.error("❌ No email transporter configured");
    return false;
  }

  try {
    const mailOptions: nodemailer.SendMailOptions = {
      from: `"Learniify" <${WORKMAIL_EMAIL}>`, // Use WorkMail email as sender
      to: params.to,
      subject: params.subject,
      text: params.text,
      html: params.html,
    };

    if (params.replyTo) {
      mailOptions.replyTo = params.replyTo;
    }

    const result = await transporter.sendMail(mailOptions);
    console.log("✅ Email sent successfully to:", params.to);
    console.log("📧 Message ID:", result.messageId);
    return true;
  } catch (error) {
    console.error("❌ WorkMail SMTP error:", error);
    if (error instanceof Error) {
      console.error("Error details:", error.message);
    }
    return false;
  }
}

export async function sendContactEmail(contactData: {
  name: string;
  email: string;
  subject: string;
  message: string;
  category: string;
}): Promise<boolean> {
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Contact Form Submission</title>
    </head>
    <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
      <div style="max-width: 600px; margin: 40px auto; background: white; border-radius: 16px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #d946ef 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px; font-weight: 700;">New Contact Form Submission</h1>
          <p style="color: rgba(255,255,255,0.9); margin: 8px 0 0 0; font-size: 16px;">From Learniify Contact Form</p>
        </div>
        
        <!-- Content -->
        <div style="padding: 30px;">
          <div style="background: #f8fafc; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
            <h3 style="color: #374151; margin: 0 0 16px 0; font-size: 18px; font-weight: 600;">Contact Details</h3>
            <div style="display: grid; gap: 12px;">
              <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
                <strong style="color: #6b7280;">Name:</strong>
                <span style="color: #374151;">${contactData.name}</span>
              </div>
              <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
                <strong style="color: #6b7280;">Email:</strong>
                <span style="color: #374151;">${contactData.email}</span>
              </div>
              <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
                <strong style="color: #6b7280;">Category:</strong>
                <span style="color: #374151; text-transform: capitalize;">${
                  contactData.category
                }</span>
              </div>
              <div style="display: flex; justify-content: space-between; padding: 8px 0;">
                <strong style="color: #6b7280;">Subject:</strong>
                <span style="color: #374151;">${contactData.subject}</span>
              </div>
            </div>
          </div>
          
          <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px;">
            <h3 style="color: #374151; margin: 0 0 12px 0; font-size: 16px; font-weight: 600;">Message:</h3>
            <div style="color: #374151; line-height: 1.6; white-space: pre-wrap;">${
              contactData.message
            }</div>
          </div>
          
          <div style="margin-top: 20px; padding: 16px; background: #fef3c7; border: 1px solid #fbbf24; border-radius: 8px;">
            <p style="color: #92400e; font-size: 14px; margin: 0; text-align: center;">
              📧 Reply directly to this email to respond to ${contactData.name}
            </p>
          </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #f9fafb; padding: 20px; border-top: 1px solid #e5e7eb; text-align: center;">
          <p style="color: #9ca3af; font-size: 12px; margin: 0;">
            This email was generated from the Learniify contact form.
          </p>
          <p style="color: #9ca3af; font-size: 12px; margin: 4px 0 0 0;">
            Submitted on ${new Date().toLocaleString()}
          </p>
        </div>
      </div>
    </body>
    </html>
  `;

  const textContent = `
New Contact Form Submission - Learniify

Contact Details:
- Name: ${contactData.name}
- Email: ${contactData.email}
- Category: ${contactData.category}
- Subject: ${contactData.subject}

Message:
${contactData.message}

---
Submitted on ${new Date().toLocaleString()}
Reply directly to this email to respond to ${contactData.name}.
  `;

  try {
    return await sendEmail({
      to: WORKMAIL_EMAIL || "<EMAIL>", // Send to WorkMail address
      from: WORKMAIL_EMAIL || "<EMAIL>", // Use WorkMail email as sender
      subject: `[${contactData.category.toUpperCase()}] ${contactData.subject}`,
      text: textContent,
      html: htmlContent,
      // Set reply-to as the user's email for easy responses
      replyTo: contactData.email,
    });
  } catch (error) {
    console.error("Contact email send error:", error);
    return false;
  }
}

export async function sendFriendInviteEmail(
  toEmail: string,
  fromUserName: string,
  inviteToken: string,
  domain: string
): Promise<boolean> {
  // Handle localhost development
  const protocol = domain.includes('localhost') ? 'http' : 'https';
  const inviteUrl = `${protocol}://${domain}/invite/${inviteToken}`;

  const htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>You're Invited to Join Learniify!</title>
    </head>
    <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
      <div style="max-width: 600px; margin: 40px auto; background: white; border-radius: 16px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #d946ef 100%); padding: 40px 30px; text-align: center;">
          <table style="margin: 0 auto 20px; border-collapse: collapse;">
            <tr>
              <td style="background: rgba(255,255,255,0.2); width: 80px; height: 80px; border-radius: 16px; text-align: center; vertical-align: middle;">
                <div style="font-size: 40px; line-height: 80px;">🎓</div>
              </td>
            </tr>
          </table>
          <h1 style="color: white; margin: 0; font-size: 32px; font-weight: 700; letter-spacing: -0.5px;">Learniify</h1>
          <p style="color: rgba(255,255,255,0.9); margin: 8px 0 0 0; font-size: 18px; font-weight: 400;">Transform Your Learning Journey</p>
        </div>
        
        <!-- Content -->
        <div style="padding: 40px 30px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h2 style="color: #1f2937; margin: 0 0 16px 0; font-size: 28px; font-weight: 700;">You're Invited! 🎉</h2>
            <p style="color: #6b7280; font-size: 18px; margin: 0; line-height: 1.6;">
              <strong style="color: #6366f1;">${fromUserName}</strong> wants you to join them on Learniify
            </p>
          </div>
          
          <!-- Features -->
          <div style="background: #f8fafc; border-radius: 12px; padding: 24px; margin: 30px 0;">
            <h3 style="color: #374151; margin: 0 0 20px 0; font-size: 20px; font-weight: 600; text-align: center;">What awaits you:</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; vertical-align: top;">
                  <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                      <td style="width: 44px; vertical-align: middle; padding-right: 12px;">
                        <table style="background: #6366f1; width: 32px; height: 32px; border-radius: 8px; border-collapse: collapse;">
                          <tr>
                            <td style="text-align: center; vertical-align: middle; font-size: 16px; line-height: 32px;">📚</td>
                          </tr>
                        </table>
                      </td>
                      <td style="vertical-align: top;">
                        <span style="color: #374151; font-size: 16px; line-height: 1.4;">Create personalized learning plans</span>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <tr>
                <td style="padding: 8px 0; vertical-align: top;">
                  <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                      <td style="width: 44px; vertical-align: middle; padding-right: 12px;">
                        <table style="background: #8b5cf6; width: 32px; height: 32px; border-radius: 8px; border-collapse: collapse;">
                          <tr>
                            <td style="text-align: center; vertical-align: middle; font-size: 16px; line-height: 32px;">📊</td>
                          </tr>
                        </table>
                      </td>
                      <td style="vertical-align: top;">
                        <span style="color: #374151; font-size: 16px; line-height: 1.4;">Track progress with detailed analytics</span>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <tr>
                <td style="padding: 8px 0; vertical-align: top;">
                  <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                      <td style="width: 44px; vertical-align: middle; padding-right: 12px;">
                        <table style="background: #d946ef; width: 32px; height: 32px; border-radius: 8px; border-collapse: collapse;">
                          <tr>
                            <td style="text-align: center; vertical-align: middle; font-size: 16px; line-height: 32px;">👥</td>
                          </tr>
                        </table>
                      </td>
                      <td style="vertical-align: top;">
                        <span style="color: #374151; font-size: 16px; line-height: 1.4;">Connect with friends and learn together</span>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <tr>
                <td style="padding: 8px 0; vertical-align: top;">
                  <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                      <td style="width: 44px; vertical-align: middle; padding-right: 12px;">
                        <table style="background: #06b6d4; width: 32px; height: 32px; border-radius: 8px; border-collapse: collapse;">
                          <tr>
                            <td style="text-align: center; vertical-align: middle; font-size: 16px; line-height: 32px;">🤖</td>
                          </tr>
                        </table>
                      </td>
                      <td style="vertical-align: top;">
                        <span style="color: #374151; font-size: 16px; line-height: 1.4;">AI-powered content recommendations</span>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </div>
          
          <!-- CTA Button -->
          <div style="text-align: center; margin: 40px 0;">
            <a href="${inviteUrl}" style="background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #d946ef 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 12px; font-size: 18px; font-weight: 600; display: inline-block; box-shadow: 0 4px 14px rgba(99, 102, 241, 0.4); transition: all 0.2s;">
              Join Learniify Now →
            </a>
          </div>
          
          <!-- Invitation Details -->
          <div style="background: #fef3c7; border: 1px solid #fbbf24; border-radius: 8px; padding: 16px; margin: 30px 0;">
            <p style="color: #92400e; font-size: 14px; margin: 0; text-align: center;">
              ⏰ This invitation expires in <strong>7 days</strong>
            </p>
          </div>
          
          <!-- Alternative Link -->
          <div style="margin: 30px 0;">
            <p style="color: #6b7280; font-size: 14px; margin: 0 0 8px 0;">
              Can't click the button? Copy and paste this link:
            </p>
            <div style="background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 6px; padding: 12px; word-break: break-all;">
              <code style="color: #374151; font-size: 13px; font-family: 'Monaco', 'Menlo', monospace;">
                ${inviteUrl}
              </code>
            </div>
          </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #f9fafb; padding: 24px 30px; border-top: 1px solid #e5e7eb;">
          <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 0;">
            This email was sent by Learniify. If you didn't expect this invitation, you can safely ignore this email.
          </p>
          <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 8px 0 0 0;">
            © 2025 Learniify. All rights reserved.
          </p>
        </div>
      </div>
    </body>
    </html>
  `;

  const textContent = `
🎉 You're Invited to Join Learniify!

${fromUserName} has invited you to join Learniify - a powerful learning platform where you can:

📚 Create personalized learning plans
📊 Track your progress with detailed analytics  
👥 Connect with friends and learn together
🤖 Access AI-powered content recommendations

Join now: ${inviteUrl}

⏰ This invitation expires in 7 days.

If you can't click the link above, copy and paste this URL into your browser:
${inviteUrl}

---
This email was sent by Learniify. If you didn't expect this invitation, you can safely ignore this email.
  `;

  try {
    console.log("Attempting to send friend invite email to:", toEmail);
    
    // Check if WorkMail is configured
    if (!transporter) {
      console.warn("⚠️ WorkMail not configured - simulating email send");
      console.log(`📧 [SIMULATED] Friend invite email to: ${toEmail}`);
      console.log(`📧 [SIMULATED] From: ${fromUserName}`);
      console.log(`📧 [SIMULATED] Invite URL: ${inviteUrl}`);
      return true; // Return success for development
    }
    
    const result = await sendEmail({
      to: toEmail,
      from: WORKMAIL_EMAIL || "<EMAIL>",
      subject: `🎉 ${fromUserName} invited you to join Learniify!`,
      text: textContent,
      html: htmlContent,
    });
    console.log("Friend invite email send result:", result);
    return result;
  } catch (error) {
    console.error("Friend invite email send error:", error);
    if (error instanceof Error) {
      console.error("Error details:", error.message);
    }
    // Return true in development to prevent blocking
    return process.env.NODE_ENV !== 'production';
  }
}

export async function sendPlanShareEmail(
  toEmail: string,
  fromUserName: string,
  planTitle: string,
  shareUrl: string
): Promise<boolean> {
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Learning Plan Shared with You</title>
    </head>
    <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
      <div style="max-width: 600px; margin: 40px auto; background: white; border-radius: 16px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 30px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 32px; font-weight: 700;">📚 Learniify</h1>
          <p style="color: rgba(255,255,255,0.9); margin: 8px 0 0 0; font-size: 18px;">Learn. Grow. Achieve.</p>
        </div>
        
        <!-- Content -->
        <div style="padding: 40px 30px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h2 style="color: #1f2937; margin: 0 0 16px 0; font-size: 28px; font-weight: 700;">🎉 You've Got a Learning Plan!</h2>
            <p style="color: #6b7280; font-size: 18px; margin: 0; line-height: 1.6;">
              <strong style="color: #667eea;">${fromUserName}</strong> shared "<strong>${planTitle}</strong>" with you
            </p>
          </div>
          
          <div style="background: #f8fafc; border-radius: 12px; padding: 25px; margin-bottom: 25px;">
            <h3 style="color: #1e293b; margin: 0 0 15px 0; font-size: 20px; font-weight: 600;">📋 Plan Details</h3>
            <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
              <strong style="color: #6b7280;">Plan:</strong>
              <span style="color: #374151;">${planTitle}</span>
            </div>
            <div style="display: flex; justify-content: space-between; padding: 8px 0;">
              <strong style="color: #6b7280;">Shared by:</strong>
              <span style="color: #374151;">${fromUserName}</span>
            </div>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${shareUrl}" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 12px; font-size: 18px; font-weight: 600; display: inline-block; box-shadow: 0 4px 14px rgba(102, 126, 234, 0.4);">
              🚀 View Learning Plan
            </a>
          </div>
          
          <div style="background: #fef3c7; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
            <h4 style="color: #92400e; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">💡 What's Next?</h4>
            <ul style="color: #92400e; margin: 0; padding-left: 20px; line-height: 1.6;">
              <li>Click the button above to view the shared plan</li>
              <li>Sign up for Learniify if you don't have an account</li>
              <li>Add the plan to your personal library</li>
              <li>Start your learning journey!</li>
            </ul>
          </div>
          
          <div style="margin: 30px 0;">
            <p style="color: #6b7280; font-size: 14px; margin: 0 0 8px 0;">
              Can't click the button? Copy and paste this link:
            </p>
            <div style="background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 6px; padding: 12px; word-break: break-all;">
              <code style="color: #374151; font-size: 13px; font-family: 'Monaco', 'Menlo', monospace;">
                ${shareUrl}
              </code>
            </div>
          </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #f9fafb; padding: 24px 30px; border-top: 1px solid #e5e7eb; text-align: center;">
          <p style="color: #9ca3af; font-size: 14px; margin: 0 0 10px 0;">Happy Learning! 🌟</p>
          <p style="color: #9ca3af; font-size: 12px; margin: 0;">© 2025 Learniify. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const textContent = `
🎉 You've Got a Learning Plan!

${fromUserName} shared "${planTitle}" with you on Learniify.

What's Next:
• Click the link below to view the shared plan
• Sign up for Learniify if you don't have an account  
• Add the plan to your personal library
• Start your learning journey!

View Learning Plan: ${shareUrl}

Happy Learning! 🌟
The Learniify Team
  `;

  try {
    console.log("Attempting to send plan share email to:", toEmail);
    
    const result = await sendEmail({
      to: toEmail,
      from: WORKMAIL_EMAIL || "<EMAIL>",
      subject: `📚 ${fromUserName} shared a learning plan with you on Learniify`,
      text: textContent,
      html: htmlContent,
    });
    
    console.log("Plan share email send result:", result);
    return result;
  } catch (error) {
    console.error("Plan share email send error:", error);
    return process.env.NODE_ENV !== 'production';
  }
}
