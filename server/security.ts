import crypto from 'crypto';
import { Request, Response, NextFunction } from 'express';

// Input sanitization for XSS prevention
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return '';
  
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .replace(/script/gi, '') // Remove script tags
    .trim();
}

// HTML encoding for XSS prevention
export function escapeHtml(unsafe: string): string {
  if (typeof unsafe !== 'string') return '';
  
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

// Log sanitization to prevent log injection
export function sanitizeForLog(input: any): string {
  if (typeof input !== 'string') {
    input = String(input);
  }
  
  return input
    .replace(/[\r\n]/g, '_') // Replace newlines with underscores
    .replace(/[\t]/g, ' ') // Replace tabs with spaces
    .substring(0, 1000); // Limit length
}

// CSRF token generation and validation
const csrfTokens = new Map<string, { token: string; expires: number }>();

export function generateCSRFToken(sessionId: string): string {
  const token = crypto.randomBytes(32).toString('hex');
  const expires = Date.now() + (60 * 60 * 1000); // 1 hour
  
  csrfTokens.set(sessionId, { token, expires });
  
  // Clean up expired tokens
  for (const [id, data] of csrfTokens.entries()) {
    if (data.expires < Date.now()) {
      csrfTokens.delete(id);
    }
  }
  
  return token;
}

export function validateCSRFToken(sessionId: string, token: string): boolean {
  const stored = csrfTokens.get(sessionId);
  if (!stored || stored.expires < Date.now()) {
    csrfTokens.delete(sessionId);
    return false;
  }
  
  return stored.token === token;
}

// CSRF middleware
export function csrfProtection(req: Request, res: Response, next: NextFunction) {
  if (req.method === 'GET' || req.method === 'HEAD' || req.method === 'OPTIONS') {
    return next();
  }

  const sessionId = req.session?.id || req.sessionID;
  const token = req.headers['x-csrf-token'] as string || req.body._csrf;

  if (!sessionId || !token || !validateCSRFToken(sessionId, token)) {
    return res.status(403).json({ error: 'Invalid CSRF token' });
  }

  next();
}

// Strict CSRF middleware for sensitive GET endpoints
export function strictCsrfProtection(req: Request, res: Response, next: NextFunction) {
  const sessionId = req.session?.id || req.sessionID;
  const token = req.headers['x-csrf-token'] as string || req.body._csrf || req.query._csrf as string;

  // For GET requests, also accept X-Requested-With header as additional protection
  const requestedWith = req.get('X-Requested-With');
  const referer = req.get('Referer');
  const origin = req.get('Origin');

  if (!sessionId) {
    return res.status(403).json({ error: 'No session found' });
  }

  // For sensitive GET endpoints, require multiple security checks
  if (req.method === 'GET' || req.method === 'HEAD') {
    // Check for X-Requested-With header (AJAX requests)
    const hasValidRequestedWith = requestedWith === 'XMLHttpRequest';

    // Check for valid referer/origin (same-origin requests)
    const hasValidOrigin = origin && (
      origin.includes('localhost') ||
      origin.includes('learniify.com') ||
      origin.includes('cloudfront.net')
    );

    const hasValidReferer = referer && (
      referer.includes('localhost') ||
      referer.includes('learniify.com') ||
      referer.includes('cloudfront.net')
    );

    if (!hasValidRequestedWith && !hasValidOrigin && !hasValidReferer) {
      return res.status(403).json({ error: 'Invalid request origin - CSRF protection' });
    }

    return next();
  }

  // For non-GET requests, require CSRF token
  if (!token || !validateCSRFToken(sessionId, token)) {
    return res.status(403).json({ error: 'Invalid CSRF token' });
  }

  next();
}

// Path traversal prevention
export function sanitizePath(userPath: string): string {
  if (typeof userPath !== 'string') return '';
  
  // Remove path traversal attempts
  return userPath
    .replace(/\.\./g, '') // Remove ..
    .replace(/[\/\\]/g, '') // Remove slashes
    .replace(/[<>:"|?*]/g, '') // Remove invalid filename chars
    .trim();
}

// Rate limiting store
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export function rateLimit(maxRequests: number, windowMs: number) {
  return (req: Request, res: Response, next: NextFunction) => {
    const key = req.ip || req.connection.remoteAddress || 'unknown';
    const now = Date.now();
    
    let record = rateLimitStore.get(key);
    
    if (!record || now > record.resetTime) {
      record = { count: 1, resetTime: now + windowMs };
      rateLimitStore.set(key, record);
      return next();
    }
    
    if (record.count >= maxRequests) {
      return res.status(429).json({ error: 'Too many requests' });
    }
    
    record.count++;
    next();
  };
}

// Secure headers middleware
export function securityHeaders(req: Request, res: Response, next: NextFunction) {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
  next();
}