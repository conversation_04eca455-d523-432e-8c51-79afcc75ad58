// Plan privacy and sharing methods for DatabaseStorage class
import { db } from "./db";
import { learningPlans, planShares, users } from "@shared/schema";
import { eq, and, or, sql } from "drizzle-orm";

// Generate a unique slug for a plan based on title
export async function generatePlanSlug(title: string): Promise<string> {
  // Create base slug from title
  const baseSlug = title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-") // Replace multiple hyphens with single
    .trim()
    .substring(0, 50); // Limit length

  // Check if slug exists and make it unique
  let slug = baseSlug;
  let counter = 1;

  while (true) {
    const existing = await db
      .select({ id: learningPlans.id })
      .from(learningPlans)
      .where(eq(learningPlans.slug, slug))
      .limit(1);

    if (existing.length === 0) {
      break;
    }

    slug = `${baseSlug}-${counter}`;
    counter++;
  }

  return slug;
}

// Share a plan with another user
export async function sharePlan(
  planId: number,
  sharedByUserId: string,
  sharedWithUserId: string,
  permissions: { canAddVideos?: boolean; canReshare?: boolean }
): Promise<void> {
  await db
    .insert(planShares)
    .values({
      planId,
      sharedByUserId,
      sharedWithUserId,
      canAddVideos: permissions.canAddVideos || false,
      canReshare: permissions.canReshare || false,
    })
    .onConflictDoUpdate({
      target: [planShares.planId, planShares.sharedWithUserId],
      set: {
        canAddVideos: permissions.canAddVideos || false,
        canReshare: permissions.canReshare || false,
        updatedAt: new Date(),
      },
    });
}

// Remove plan sharing
export async function unsharePlan(
  planId: number,
  sharedWithUserId: string
): Promise<void> {
  await db
    .delete(planShares)
    .where(
      and(
        eq(planShares.planId, planId),
        eq(planShares.sharedWithUserId, sharedWithUserId)
      )
    );
}

// Get all users a plan is shared with
export async function getPlanShares(planId: number): Promise<any[]> {
  return await db
    .select({
      id: planShares.id,
      sharedWithUserId: planShares.sharedWithUserId,
      canAddVideos: planShares.canAddVideos,
      canReshare: planShares.canReshare,
      createdAt: planShares.createdAt,
      user: {
        id: users.id,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        profileImageUrl: users.profileImageUrl,
      },
    })
    .from(planShares)
    .innerJoin(users, eq(planShares.sharedWithUserId, users.id))
    .where(eq(planShares.planId, planId));
}

// Get plans shared with a user
export async function getUserSharedPlans(userId: string): Promise<any[]> {
  return await db
    .select({
      id: learningPlans.id,
      title: learningPlans.title,
      description: learningPlans.description,
      slug: learningPlans.slug,
      isPublic: learningPlans.isPublic,
      createdAt: learningPlans.createdAt,
      updatedAt: learningPlans.updatedAt,
      owner: {
        id: users.id,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        profileImageUrl: users.profileImageUrl,
      },
      permissions: {
        canAddVideos: planShares.canAddVideos,
        canReshare: planShares.canReshare,
      },
    })
    .from(planShares)
    .innerJoin(learningPlans, eq(planShares.planId, learningPlans.id))
    .innerJoin(users, eq(learningPlans.userId, users.id))
    .where(eq(planShares.sharedWithUserId, userId));
}

// Check user's access permissions for a plan
export async function canUserAccessPlan(
  planId: number,
  userId: string
): Promise<{
  canView: boolean;
  canAddVideos: boolean;
  canShare: boolean;
  isOwner: boolean;
}> {
  // Get the plan
  const [plan] = await db
    .select({
      id: learningPlans.id,
      userId: learningPlans.userId,
      isPublic: learningPlans.isPublic,
    })
    .from(learningPlans)
    .where(eq(learningPlans.id, planId))
    .limit(1);

  if (!plan) {
    return {
      canView: false,
      canAddVideos: false,
      canShare: false,
      isOwner: false,
    };
  }

  // Check if user is the owner
  const isOwner = plan.userId === userId;
  if (isOwner) {
    return {
      canView: true,
      canAddVideos: true,
      canShare: true,
      isOwner: true,
    };
  }

  // If it's a public plan, anyone can view and add videos
  if (plan.isPublic) {
    return {
      canView: true,
      canAddVideos: true,
      canShare: true,
      isOwner: false,
    };
  }

  // For private plans, check sharing permissions
  const [sharePermission] = await db
    .select({
      canAddVideos: planShares.canAddVideos,
      canReshare: planShares.canReshare,
    })
    .from(planShares)
    .where(
      and(
        eq(planShares.planId, planId),
        eq(planShares.sharedWithUserId, userId)
      )
    )
    .limit(1);

  if (sharePermission) {
    return {
      canView: true,
      canAddVideos: sharePermission.canAddVideos,
      canShare: sharePermission.canReshare,
      isOwner: false,
    };
  }

  // No access
  return {
    canView: false,
    canAddVideos: false,
    canShare: false,
    isOwner: false,
  };
}
