import nodemailer from "nodemailer";

// Amazon WorkMail SMTP Configuration
const createTransporter = () => {
  const port = parseInt(process.env.WORKMAIL_SMTP_PORT || "587");
  return nodemailer.createTransport({
    host: process.env.WORKMAIL_SMTP_HOST || "smtp.mail.us-east-1.awsapps.com",
    port: port,
    secure: port === 465, // true for 465, false for other ports
    requireTLS: true,
    auth: {
      user: process.env.WORKMAIL_EMAIL || "<EMAIL>",
      pass: process.env.WORKMAIL_PASSWORD,
    },
    connectionTimeout: 10000, // 10 seconds
    greetingTimeout: 5000, // 5 seconds
    socketTimeout: 10000, // 10 seconds
    debug: true, // Enable debug logging
  });
};

// Modern HTML email template for password reset
const createPasswordResetEmailTemplate = (
  resetUrl: string,
  firstName: string
) => {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Learniify Password</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
        }
        
        .logo {
            font-size: 32px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 10px;
        }
        
        .header-subtitle {
            color: #e2e8f0;
            font-size: 16px;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 24px;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 20px;
        }
        
        .message {
            font-size: 16px;
            color: #4a5568;
            margin-bottom: 30px;
            line-height: 1.7;
        }
        
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            transition: transform 0.2s ease;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .reset-button:hover {
            transform: translateY(-2px);
        }
        
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .security-notice {
            background-color: #f7fafc;
            border-left: 4px solid #4299e1;
            padding: 20px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .security-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .security-text {
            font-size: 14px;
            color: #4a5568;
        }
        
        .footer {
            background-color: #f8fafc;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-text {
            font-size: 14px;
            color: #718096;
            margin-bottom: 10px;
        }
        
        .footer-link {
            color: #667eea;
            text-decoration: none;
        }
        
        .expiry-notice {
            background-color: #fef5e7;
            border: 1px solid #f6ad55;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            text-align: center;
        }
        
        .expiry-text {
            color: #c05621;
            font-size: 14px;
            font-weight: 500;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 0 10px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .greeting {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">Learniify</div>
            <div class="header-subtitle">Your Learning Journey Continues</div>
        </div>
        
        <div class="content">
            <div class="greeting">Hi ${firstName || "there"}! 👋</div>
            
            <div class="message">
                We received a request to reset your password for your Learniify account. 
                Don't worry, it happens to the best of us! Click the button below to create a new password.
            </div>
            
            <div class="button-container">
                <a href="${resetUrl}" class="reset-button">Reset My Password</a>
            </div>
            
            <div class="expiry-notice">
                <div class="expiry-text">⏰ This link will expire in 1 hour for security reasons</div>
            </div>
            
            <div class="security-notice">
                <div class="security-title">🔒 Security Notice</div>
                <div class="security-text">
                    If you didn't request this password reset, please ignore this email. 
                    Your account is safe and no changes have been made.
                </div>
            </div>
            
            <div class="message">
                If the button above doesn't work, you can copy and paste this link into your browser:
                <br><br>
                <a href="${resetUrl}" style="color: #667eea; word-break: break-all;">${resetUrl}</a>
            </div>
        </div>
        
        <div class="footer">
            <div class="footer-text">
                This email was sent by Learniify. If you have any questions, 
                <a href="mailto:<EMAIL>" class="footer-link">contact our support team</a>.
            </div>
            <div class="footer-text">
                © ${new Date().getFullYear()} Learniify. All rights reserved.
            </div>
        </div>
    </div>
</body>
</html>
  `;
};

// Send password reset email
export const sendPasswordResetEmail = async (
  email: string,
  resetToken: string,
  firstName?: string
) => {
  const transporter = createTransporter();

  // Create reset URL - you can change this to your preferred domain
  const resetUrl = `${
    process.env.FRONTEND_URL || "https://www.learniify.com"
  }/reset-password?token=${resetToken}`;

  const mailOptions = {
    from: {
      name: "Learniify Support",
      address: process.env.WORKMAIL_EMAIL || "<EMAIL>",
    },
    to: email,
    subject: "🔐 Reset Your Learniify Password",
    html: createPasswordResetEmailTemplate(resetUrl, firstName || ""),
    text: `Hi ${firstName || "there"}!

We received a request to reset your password for your Learniify account.

Click this link to reset your password: ${resetUrl}

This link will expire in 1 hour for security reasons.

If you didn't request this password reset, please ignore this email. Your account is safe and no changes have been made.

Best regards,
The Learniify Team

© ${new Date().getFullYear()} Learniify. All rights reserved.`,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log("Password reset email sent:", info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error("Error sending password reset email:", error);
    throw new Error("Failed to send password reset email");
  }
};

// Test email configuration
export const testEmailConfiguration = async () => {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    console.log("Email configuration is valid");
    return true;
  } catch (error) {
    console.error("Email configuration error:", error);
    return false;
  }
};
