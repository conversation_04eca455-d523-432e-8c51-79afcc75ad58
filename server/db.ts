import { Pool } from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import * as schema from "@shared/schema";

if (!process.env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Did you forget to provision a database?",
  );
}

const isNeonDB = process.env.DATABASE_URL?.includes('neon.tech');

export const pool = new Pool({ 
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false },
  max: 10, // Maximum number of connections in the pool
  min: 2,  // Minimum number of connections in the pool
  idleTimeoutMillis: 30000, // Close idle connections after 30 seconds
  connectionTimeoutMillis: 10000, // Return error after 10 seconds if connection could not be established
  acquireTimeoutMillis: 60000, // Return error after 60 seconds if connection could not be acquired
  allowExitOnIdle: true // Allow the pool to close all connections and exit
});

// Set search_path for Neon DB after connection
if (isNeonDB) {
  pool.on('connect', (client) => {
    client.query('SET search_path TO public');
  });
}

// Handle pool errors
pool.on('error', (err) => {
  console.error('❌ Database pool error:', err);
  // Don't exit the process, just log the error
});

// Handle connection errors
pool.on('connect', (client) => {
  client.on('error', (err) => {
    console.error('❌ Database client error:', err);
  });
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🔄 Closing database pool...');
  await pool.end();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🔄 Closing database pool...');
  await pool.end();
  process.exit(0);
});

// Periodic connection health check
setInterval(async () => {
  const isHealthy = await ensureConnection();
  if (!isHealthy) {
    console.log('⚠️ Database connection unhealthy, but continuing...');
  }
}, 60000); // Check every minute
export const db = drizzle({ client: pool, schema });

// Database health check and reconnection
export const ensureConnection = async () => {
  try {
    await pool.query('SELECT 1');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
};

// Retry wrapper for database operations
export const withRetry = async <T>(operation: () => Promise<T>, maxRetries = 3): Promise<T> => {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      console.error(`❌ Database operation failed (attempt ${attempt}/${maxRetries}):`, error.message);
      
      // If it's a connection error, wait before retrying
      if (error.code === 'XX000' || error.message?.includes('shutdown') || error.message?.includes('termination')) {
        console.log(`🔄 Waiting 2 seconds before retry...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      } else {
        // For other errors, don't retry
        throw error;
      }
    }
  }
  
  throw lastError;
};